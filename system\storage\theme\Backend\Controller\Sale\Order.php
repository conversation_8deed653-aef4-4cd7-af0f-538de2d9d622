<?php

namespace Theme25\Backend\Controller\Sale;

class Order extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'sale/order');

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     *
     * ЗАБЕЛЕЖКА: JavaScript файловете се зареждат от специализираните суб-контролери:
     * - Index.php зарежда order-list.js
     * - Info.php зарежда order-info.js
     * - Edit.php зарежда order-edit.js
     */
    protected function loadScripts() {
        // Не зареждаме JavaScript файлове тук, тъй като всеки суб-контролер
        // зарежда своя специализиран JavaScript файл
    }

	public function index() {
		$this->setTitle('Поръчки');

		// Инициализиране на данните
		$this->initAdminData();

		try {
			$subController = $this->setBackendSubController('Sale/Order/Index', $this);

			// Подготовка на данните
			$subController->prepareData();

			// Добавяне на основни данни ако липсват
			if (!isset($this->data['orders'])) {
				$this->setData('orders', []);
			}
			if (!isset($this->data['status_options'])) {
				// Зареждане на статуси от базата данни
				$this->loadModelAs('localisation/order_status', 'orderStatuses');
				$order_statuses = $this->orderStatuses->getOrderStatuses();

				$status_options = [['value' => '', 'text' => 'Всички статуси']];
				foreach ($order_statuses as $status) {
					$status_options[] = [
						'value' => $status['order_status_id'],
						'text' => $status['name']
					];
				}
				$this->setData('status_options', $status_options);
			}
			if (!isset($this->data['sort_options'])) {
				$this->setData('sort_options', [
					['value' => 'o.order_id-DESC', 'text' => 'Последно добавени'],
					['value' => 'o.order_id-ASC', 'text' => 'Най-стари']
				]);
			}
			if (!isset($this->data['pagination_html'])) {
				$this->setData('pagination_html', '');
			}

		} catch (Exception $e) {
			// Ако има грешка, задаваме минимални данни
			$this->setData([
				'orders' => [],
				'status_options' => [['value' => '', 'text' => 'Всички статуси']],
				'sort_options' => [
					['value' => 'o.order_id-DESC', 'text' => 'Последно добавени'],
					['value' => 'o.order_id-ASC', 'text' => 'Най-стари']
				],
				'pagination_html' => '',
				'add_new_url' => $this->getAdminLink('sale/order/add')
			]);
		}

		// Рендиране на шаблона с данните от $this->data
		$this->renderTemplateWithDataAndOutput('sale/order');
	}

	public function info() {
		$this->setTitle('Детайли на поръчка');

		// Инициализиране на данните
		$this->initAdminData();

		$subController = $this->setBackendSubController('Sale/Order/Info', $this);

		// Подготовка на данните
		$subController->prepareOrderInfo();

		$this->renderTemplateWithDataAndOutput('sale/order_info');
	}

	public function edit() {
		$this->setTitle('Редакция на поръчка');

		// Инициализиране на данните
		$this->initAdminData();

		$subController = $this->setBackendSubController('Sale/Order/Edit', $this);

		// Подготовка на данните
		$subController->prepareOrderForm();

		$this->renderTemplateWithDataAndOutput('sale/order_form');
	}

	/**
     * Обработва AJAX заявките за автодопълване
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        if ($this->requestGet('type') && $this->requestGet('type')) {
            $type = $this->requestGet('type');

            // Зареждане на съответния субконтролер
            $sub_controller = $this->setBackendSubController('Sale/Order/' . ucfirst($type) . 'Autocomplete', $this);

            if ($sub_controller && method_exists($sub_controller, 'autocomplete')) {
                $json = $sub_controller->autocomplete($this->requestGet());
            } else {
                $json['error'] = 'Методът не е намерен';
            }
        } else {
            $json['error'] = 'Липсващ параметър type';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

	/**
     * Обработва AJAX заявките за промяна на статус (разпределителен метод)
     */
    public function updateStatus() {
        $subController = $this->setBackendSubController('Sale/Order/StatusUpdate', $this);
        $subController->updateStatus();
    }

	/**
     * Изтрива поръчки
     */
    public function delete() {
        if ($this->isPostRequest() && $this->hasPermission('modify', 'sale/order')) {
            $selected = $this->requestPost('selected', []);

            if (!empty($selected)) {
                $this->loadModel('sale/order');

                foreach ($selected as $order_id) {
                    $this->model_sale_order->deleteOrder((int)$order_id);
                }

                $this->setSession('success', 'Поръчките са изтрити успешно');
            }
        }

        $this->redirectResponse($this->getAdminLink('sale/order'));
    }

    /**
     * Изпраща известие за поръчка (разпределителен метод)
     */
    public function sendNotification() {
        $subController = $this->setBackendSubController('Sale/Order/Notification', $this);
        $subController->sendNotification();
    }

    /**
     * Експортира поръчки в CSV формат (разпределителен метод)
     */
    public function export() {
        $subController = $this->setBackendSubController('Sale/Order/Export', $this);
        $subController->export();
    }

    /**
     * Експортира поръчки в Excel формат (разпределителен метод)
     */
    public function exportExcel() {
        $subController = $this->setBackendSubController('Sale/Order/Export', $this);
        $subController->exportExcel();
    }

    /**
     * Групова промяна на статус (разпределителен метод)
     */
    public function bulkUpdateStatus() {
        $subController = $this->setBackendSubController('Sale/Order/StatusUpdate', $this);
        $subController->bulkUpdateStatus();
    }

    /**
     * Групово изпращане на известия (разпределителен метод)
     */
    public function sendBulkNotification() {
        $subController = $this->setBackendSubController('Sale/Order/Notification', $this);
        $subController->sendBulkNotification();
    }

    /**
     * Получава шаблони за известия (разпределителен метод)
     */
    public function getNotificationTemplates() {
        $subController = $this->setBackendSubController('Sale/Order/Notification', $this);
        $subController->getNotificationTemplates();
    }

    /**
     * AJAX метод за търсене на продукти (разпределителен метод)
     */
    public function searchProducts() {
        $subController = $this->setBackendSubController('Sale/Order/Edit', $this);
        $subController->searchProducts();
    }

}
