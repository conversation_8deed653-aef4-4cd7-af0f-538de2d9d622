<?php

namespace Theme25\Backend\Controller\Marketing\Contact;

use Theme25\Helper\MailHelper;

class Send extends \Theme25\ControllerSubMethods {

    /**
     * Изпраща мейли до избраните групи клиенти
     *
     * @param array $postData POST данни от формата
     * @return array JSON отговор
     */
    public function sendEmails($postData) {
        $json = [];

        // Валидация на данните
        if (!$this->validateEmailData($postData, $json)) {
            return $json;
        }

        try {
            // Зареждане на необходимите модели
            $this->loadModelsAs([
                'setting/store' => 'storeModel',
                'setting/setting' => 'settingModel',
                'customer/customer' => 'customerModel',
                'marketing/contact' => 'contactModel'
            ]);

            // Получаване на информация за магазина
            $store_info = $this->getStoreInfo($postData['store_id']);

            // Получаване на мейл адресите според избрания тип
            $emails = $this->getEmailsByType($postData);
            
            if (empty($emails)) {
                $json['error']['email'] = 'Няма намерени мейл адреси за изпращане';
                return $json;
            }

            // Изпращане на мейлите
            $processed_count = $this->sendEmailsToRecipients($emails, $postData, $store_info);

            // Записване в базата данни
            $this->saveEmailRecord($postData, $processed_count);

            // Съобщение за успех - всички мейли се добавят в опашката
            $json['success'] = "Успешно добавени {$processed_count} мейла в опашката за изпращане. Мейлите ще бъдат изпратени автоматично.";

        } catch (Exception $e) {
            $json['error']['warning'] = 'Грешка при изпращане на мейлите: ' . $e->getMessage();
        }

        return $json;
    }

    /**
     * Валидира данните от формата
     *
     * @param array $postData POST данни
     * @param array $json JSON отговор за грешки
     * @return bool
     */
    private function validateEmailData($postData, &$json) {
        if (empty($postData['subject'])) {
            $json['error']['subject'] = 'Полето за тема е задължително';
        }

        if (empty($postData['message'])) {
            $json['error']['message'] = 'Полето за съобщение е задължително';
        }

        if (empty($postData['to'])) {
            $json['error']['to'] = 'Моля, изберете получатели';
        }

        return empty($json['error']);
    }

    /**
     * Получава информация за магазина
     *
     * @param int $store_id ID на магазина
     * @return array
     */
    private function getStoreInfo($store_id) {
        if ($store_id > 0) {
            $store_info = $this->storeModel->getStore($store_id);
            $store_name = $store_info['name'];
        } else {
            $store_name = $this->getConfig('config_name');
        }

        $setting = $this->settingModel->getSetting('config', $store_id);


        F()->log->developer($setting, __FILE__, __LINE__);
        
        $store_email = isset($setting['config_email']) ? $setting['config_email'] : $this->getConfig('config_email');

        return [
            'name' => $store_name,
            'email' => $store_email
        ];
    }

    /**
     * Получава мейл адресите според избрания тип
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getEmailsByType($postData) {
        $emails = [];

        switch ($postData['to']) {
            case 'newsletter':
                $emails = $this->getNewsletterEmails();
                break;
            case 'customer_all':
                $emails = $this->getAllCustomerEmails();
                break;
            case 'customer_group':
                $emails = $this->getCustomerGroupEmails($postData['customer_group_id']);
                break;
            case 'customer':
                $emails = $this->getSelectedCustomerEmails($postData['customer']);
                break;
        }

        return array_unique($emails);
    }

    /**
     * Получава мейлите на клиентите абонирани за newsletter
     *
     * @return array
     */
    private function getNewsletterEmails() {
        $customer_data = [
            'filter_newsletter' => 1
        ];

        $results = $this->customerModel->getCustomers($customer_data);
        return array_column($results, 'email');
    }

    /**
     * Получава мейлите на всички клиенти
     *
     * @return array
     */
    private function getAllCustomerEmails() {
        $results = $this->customerModel->getCustomers([]);
        return array_column($results, 'email');
    }

    /**
     * Получава мейлите на клиентите от определена група
     *
     * @param int $customer_group_id ID на клиентската група
     * @return array
     */
    private function getCustomerGroupEmails($customer_group_id) {
        $customer_data = [
            'filter_customer_group_id' => $customer_group_id
        ];

        $results = $this->customerModel->getCustomers($customer_data);
        return array_column($results, 'email');
    }

    /**
     * Получава мейлите на избраните клиенти
     *
     * @param array $customer_ids Масив с ID-та на клиентите
     * @return array
     */
    private function getSelectedCustomerEmails($customer_ids) {
        $emails = [];
        
        if (!empty($customer_ids)) {
            foreach ($customer_ids as $customer_id) {
                $customer_info = $this->customerModel->getCustomer($customer_id);
                if ($customer_info) {
                    $emails[] = $customer_info['email'];
                }
            }
        }

        return $emails;
    }

    /**
     * Изпраща мейлите до получателите чрез queue системата
     *
     * @param array $emails Масив с мейл адреси
     * @param array $postData POST данни
     * @param array $store_info Информация за магазина
     * @return int Брой добавени в опашка мейли
     */
    private function sendEmailsToRecipients($emails, $postData, $store_info) {
        $sent_count = 0;
        $queued_count = 0;
        $total_emails = count($emails);

        // Подготвяне на HTML съобщението
        $message = $this->prepareHtmlMessage($postData['subject'], $postData['message']);

        $subject = html_entity_decode($postData['subject'], ENT_QUOTES, 'UTF-8');

        // Подготовка на mail опциите
        $mailOptions = [
            // 'adaptor' => $this->getConfig('config_mail_engine', 'mail'),
            'from_email' => $store_info['email'],
            'sender' => html_entity_decode($store_info['name'], ENT_QUOTES, 'UTF-8')
        ];

        // // Добавяне на SMTP настройки ако се използва SMTP
        // if ($mailOptions['adaptor'] === 'smtp') {
        //     $mailOptions['smtp'] = [
        //         'hostname' => $this->getConfig('config_mail_smtp_hostname'),
        //         'username' => $this->getConfig('config_mail_smtp_username'),
        //         'password' => html_entity_decode($this->getConfig('config_mail_smtp_password'), ENT_QUOTES, 'UTF-8'),
        //         'port' => $this->getConfig('config_mail_smtp_port'),
        //         'timeout' => $this->getConfig('config_mail_smtp_timeout')
        //     ];
        // }

        // Оптимизация за масови мейли (повече от 5 получателя)
        if ($total_emails > 5) {
            // Създаваме масово съобщение за оптимизирано съхранение
            $messageId = MailHelper::createMassMessage($subject, $message);

            if ($messageId !== false) {
                // Добавяме всички мейли с референция към масовото съобщение
                foreach ($emails as $email) {
                    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $mailMessage = [
                            'to' => $email,
                            'from' => $store_info['email'],
                            'sender' => html_entity_decode($store_info['name'], ENT_QUOTES, 'UTF-8'),
                            'subject' => $subject,
                            'html' => '' // Празно, защото използваме message_id
                        ];

                        $result = MailHelper::addToQueueWithMassMessage($mailOptions, $mailMessage, $messageId, 1);

                        if (is_numeric($result)) {
                            $queued_count++;
                        }
                    }
                }
            } else {
                // Fallback към стандартния метод ако създаването на масовото съобщение се провали
                $queued_count = $this->sendEmailsStandard($emails, $mailOptions, $store_info, $subject, $message);
            }
        } else {
            // За малко количество мейли използваме стандартния метод
            $queued_count = $this->sendEmailsStandard($emails, $mailOptions, $store_info, $subject, $message);
        }

        return $queued_count;
    }

    /**
     * Стандартен метод за изпращане на мейли (за малко количество или fallback)
     *
     * @param array $emails Масив с мейл адреси
     * @param array $mailOptions Mail опции
     * @param array $store_info Информация за магазина
     * @param string $subject Тема на мейла
     * @param string $message HTML съдържание
     * @return int Брой добавени в опашка мейли
     */
    private function sendEmailsStandard($emails, $mailOptions, $store_info, $subject, $message) {
        $queued_count = 0;

        foreach ($emails as $email) {
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $mailMessage = [
                    'to' => $email,
                    'from' => $store_info['email'],
                    'sender' => html_entity_decode($store_info['name'], ENT_QUOTES, 'UTF-8'),
                    'subject' => $subject,
                    'html' => $message
                ];

                // Използваме MailHelper::send() който автоматично ще добави в опашката
                $result = MailHelper::send($mailOptions, $mailMessage, false, null, 1);

                if (is_numeric($result)) { // Queue ID е върнат
                    $queued_count++;
                }
            }
        }

        return $queued_count;
    }

    /**
     * Подготвя HTML съобщението
     *
     * @param string $subject Тема на мейла
     * @param string $message Съобщение
     * @return string HTML съобщение
     */
    private function prepareHtmlMessage($subject, $message) {
        $html_message = '<html dir="ltr" lang="bg">' . "\n";
        $html_message .= '  <head>' . "\n";
        $html_message .= '    <title>' . htmlspecialchars($subject) . '</title>' . "\n";
        $html_message .= '    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">' . "\n";
        $html_message .= '  </head>' . "\n";
        $html_message .= '  <body>' . html_entity_decode($message, ENT_QUOTES, 'UTF-8') . '</body>' . "\n";
        $html_message .= '</html>' . "\n";

        return $html_message;
    }

    /**
     * Изпраща единичен мейл чрез MailHelper
     *
     * @param string $email Мейл адрес
     * @param string $subject Тема
     * @param string $message HTML съобщение
     * @param array $store_info Информация за магазина
     * @return bool
     */
    private function sendSingleEmail($email, $subject, $message, $store_info) {
        try {
            // Подготовка на mail опциите
            $mailOptions = [
                'adaptor' => $this->getConfig('config_mail_engine', 'mail'),
                'from_email' => $store_info['email'],
                'sender' => html_entity_decode($store_info['name'], ENT_QUOTES, 'UTF-8')
            ];

            // Добавяне на SMTP настройки ако се използва SMTP
            if ($mailOptions['adaptor'] === 'smtp') {
                $mailOptions['smtp'] = [
                    'hostname' => $this->getConfig('config_mail_smtp_hostname'),
                    'username' => $this->getConfig('config_mail_smtp_username'),
                    'password' => html_entity_decode($this->getConfig('config_mail_smtp_password'), ENT_QUOTES, 'UTF-8'),
                    'port' => $this->getConfig('config_mail_smtp_port'),
                    'timeout' => $this->getConfig('config_mail_smtp_timeout')
                ];
            }

            // Подготовка на съобщението
            $mailMessage = [
                'to' => $email,
                'from' => $store_info['email'],
                'sender' => html_entity_decode($store_info['name'], ENT_QUOTES, 'UTF-8'),
                'subject' => html_entity_decode($subject, ENT_QUOTES, 'UTF-8'),
                'html' => $message
            ];

            // Изпращане чрез MailHelper с принудително директно изпращане
            // За единичен мейл използваме директно изпращане за по-бърз отговор
            $result = MailHelper::send($mailOptions, $mailMessage, false, null, 1, null, true);

            return $result === true;

        } catch (\Exception $e) {
            // Логване на грешката
            F()->log->error('MailHelper send error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Записва информация за изпратения мейл в базата данни
     *
     * @param array $postData POST данни
     * @param int $sent_count Брой изпратени мейли
     */
    private function saveEmailRecord($postData, $sent_count) {
        $this->contactModel->addEmailRecord([
            'subject' => $postData['subject'],
            'message' => $postData['message'],
            'recipient_type' => $postData['to'],
            'recipient_count' => $sent_count,
            'date_sent' => date('Y-m-d H:i:s')
        ]);
    }
}
