<?php
namespace Theme25\Helper;

class MailHelper  {
    protected static $logLevel = 'error'; // info, warn, error, debug
    protected static $maxLogSize = 524288; // 512KB before rotation
    protected static $captureSendmailOutput = true;
    protected static $registry;

    public function __construct() {
        self::$registry = CM()->getRegistry();
    }

    // Mail Queue конфигурация
    protected static $queueConfig = null;

    // Lock файл за предотвратяване на паралелна обработка
    protected static $lockFile = null;

    public static function create(array $options = []) {

        $options = array_merge(self::getMailOptions(), $options);

        $adaptor = 'mail';
        if (isset($options['adaptor'])) {
            $adaptor = $options['adaptor'] === 'smtp' ? 'smtp' : 'mail';
        }
        // $options['parameter'] = '-f '. $options['from_email'];
        $mail = new \Mail($adaptor);
        if ($adaptor === 'smtp') {
            self::applySmtp($mail, $options);
        } else {
            self::applyMail($mail, $options);
        }
        return $mail;
    }

    public static function getMailOptions() {
        // Подготовка на mail опциите
        $mailOptions = [
            'adaptor' => self::getConfig('config_mail_engine', 'mail'),
            'from_email' => self::getConfig('config_email'),
            'sender' => html_entity_decode(self::getConfig('config_name'), ENT_QUOTES, 'UTF-8')
        ];

        // Добавяне на SMTP настройки ако се използва SMTP
        if ($mailOptions['adaptor'] === 'smtp') {
            $mailOptions['smtp'] = [
                'hostname' => self::getConfig('config_mail_smtp_hostname'),
                'username' => self::getConfig('config_mail_smtp_username'),
                'password' => html_entity_decode(self::getConfig('config_mail_smtp_password'), ENT_QUOTES, 'UTF-8'),
                'port' => self::getConfig('config_mail_smtp_port'),
                'timeout' => self::getConfig('config_mail_smtp_timeout')
            ];
        }
        return $mailOptions;
    }

    public static function getMailOption($key) {
        $options = self::getMailOptions();

        F()->log->developer($options, __FILE__, __LINE__);
        F()->log->developer($key, __FILE__, __LINE__);
        
        return $options[$key] ?? null;
    }

    public static function sendQuick($options, $message, $throwOnError = false) {
        $mail = self::create($options);
        self::applyMessageDefaults($mail, $message);

        $result = false;
        $sendmailOutput = null;

        if (self::$captureSendmailOutput && $options['adaptor'] === 'mail') {
            $sendmailOutput = self::sendWithCapture($mail);
            $result = $sendmailOutput['success'];
        } else {
            $result = $mail->send();
        }

        self::logDebug('Send result', [
            'result' => $result,
            'sendmail_output' => $sendmailOutput,
            'last_error' => error_get_last(),
            'mail_settings' => $options,
            'message' => $message
        ]);

        if ($throwOnError && !$result) {
            throw new \Exception('Mail sending failed. See mail_debug.log for details.');
        }

        return $result;
    }

    /**
     * Изпраща мейл директно или го добавя в опашката според конфигурацията
     *
     * @param array $options Mail опции
     * @param array $message Съобщение данни
     * @param bool $throwOnError Дали да хвърля грешка при неуспех
     * @param bool $useQueue Принудително използване на опашка (null = според конфигурацията)
     * @param int $priority Приоритет за опашката (1-5)
     * @param string $scheduledAt Планирано време за изпращане
     * @param bool $forceDirectSend Принудително директно изпращане (заобикаля queue)
     * @return bool|int True/false за директно изпращане, Queue ID за опашка
     */
    public static function send(array $options, array $message, bool $throwOnError = false, bool $useQueue = null, int $priority = 1, string $scheduledAt = null, bool $forceDirectSend = false) {
        self::initQueueConfig();

        // Ако е зададено принудително директно изпращане, заобикаляме queue
        if ($forceDirectSend) {
            return self::sendQuick($options, $message, $throwOnError);
        }

        // Определяме дали да използваме опашката
        $shouldUseQueue = $useQueue !== null ? $useQueue : self::$queueConfig['use_queue'];

        if ($shouldUseQueue) {
            // Добавяме в опашката
            $queueId = self::addToQueue($options, $message, $priority, $scheduledAt);

            if ($queueId === false && $throwOnError) {
                throw new \Exception('Failed to add email to queue');
            }

            return $queueId;
        } else {
            
            // Изпращаме директно
            return self::sendQuick($options, $message, $throwOnError);
        }
    }

    /**
     * Инициализира конфигурацията на queue системата
     */
    protected static function initQueueConfig() {
        if (self::$queueConfig === null) {
            // Зареждане на конфигурация от .env файл
            $envConfig = [];

            if (class_exists('\Theme25\Data') && method_exists('\Theme25\Data', 'getInstance')) {
                $data = \Theme25\Data::getInstance();
                $envConfig = [
                    'use_queue' => $data->get('MAIL_QUEUE_ENABLED', false),
                    'max_emails_per_batch' => (int)$data->get('MAIL_QUEUE_MAX_EMAILS_PER_BATCH', 10),
                    'delay_between_emails' => (int)$data->get('MAIL_QUEUE_DELAY_BETWEEN_EMAILS', 1),
                    'max_attempts' => (int)$data->get('MAIL_QUEUE_MAX_ATTEMPTS', 3),
                    'auto_process' => $data->get('MAIL_QUEUE_AUTO_PROCESS', true),
                    'cleanup_days' => (int)$data->get('MAIL_QUEUE_CLEANUP_DAYS', 30)
                ];
            }

            // Fallback конфигурация
            self::$queueConfig = array_merge([
                'use_queue' => true, // По подразбиране използваме queue
                'max_emails_per_batch' => 10,
                'delay_between_emails' => 1,
                'max_attempts' => 3,
                'auto_process' => true,
                'cleanup_days' => 30
            ], $envConfig);
        }
    }

    /**
     * Конфигурира mail queue системата
     *
     * @param array $config Конфигурационни опции
     */
    public static function configureQueue(array $config = []) {
        self::initQueueConfig();
        self::$queueConfig = array_merge(self::$queueConfig, $config);
    }

    /**
     * Получава текущата конфигурация на queue системата
     *
     * @return array
     */
    public static function getQueueConfig() {
        self::initQueueConfig();
        return self::$queueConfig;
    }

    /**
     * Добавя мейл в опашката за изпращане
     *
     * @param array $options Mail опции (adaptor, smtp настройки и т.н.)
     * @param array $message Съобщение данни
     * @param int $priority Приоритет (1 = висок, 5 = нисък)
     * @param string $scheduledAt Планирано време за изпращане (Y-m-d H:i:s формат)
     * @return int|false Queue ID или false при грешка
     */
    public static function addToQueue(array $options, array $message, int $priority = 1, string $scheduledAt = null) {
        try {
            self::initQueueConfig();
            $db = self::getDatabase();

            $queueData = [
                'to_email' => is_array($message['to']) ? implode(',', $message['to']) : $message['to'],
                'from_email' => $message['from'] ?? $options['from_email'] ?? '',
                'sender_name' => $message['sender'] ?? $options['sender'] ?? '',
                'subject' => $message['subject'] ?? '',
                'message' => $message['html'] ?? $message['text'] ?? '',
                'priority' => max(1, min(5, $priority)),
                'created_at' => date('Y-m-d H:i:s'),
                'scheduled_at' => $scheduledAt ?? date('Y-m-d H:i:s'),
                'max_attempts' => self::$queueConfig['max_attempts']
            ];

            $sql = "INSERT INTO `" . DB_PREFIX . "mail_queue`
                    SET `to_email` = '" . $db->escape($queueData['to_email']) . "',
                        `from_email` = '" . $db->escape($queueData['from_email']) . "',
                        `sender_name` = '" . $db->escape($queueData['sender_name']) . "',
                        `subject` = '" . $db->escape($queueData['subject']) . "',
                        `message` = '" . $db->escape($queueData['message']) . "',
                        `priority` = '" . (int)$queueData['priority'] . "',
                        `created_at` = '" . $queueData['created_at'] . "',
                        `scheduled_at` = '" . $queueData['scheduled_at'] . "',
                        `max_attempts` = '" . (int)$queueData['max_attempts'] . "'";

            $db->query($sql);
            $queueId = $db->getLastId();

            self::logDebug('Email added to queue', [
                'queue_id' => $queueId,
                'to_email' => $queueData['to_email'],
                'subject' => $queueData['subject'],
                'priority' => $queueData['priority']
            ]);

            // Стартираме автоматична обработка ако не е в ход
            self::triggerAutoProcessing();

            return $queueId;

        } catch (\Exception $e) {
            self::logDebug('Failed to add email to queue', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);

            return false;
        }
    }

    /**
     * Обработва мейлите в опашката
     *
     * @param int $limit Максимален брой мейли за обработка
     * @return array Резултат от обработката
     */
    public static function processQueue(int $limit = null) {
        self::initQueueConfig();

        if ($limit === null) {
            $limit = self::$queueConfig['max_emails_per_batch'];
        }

        $db = self::getDatabase();
        $processed = 0;
        $sent = 0;
        $failed = 0;
        $errors = [];

        try {
            // Получаване на мейли за обработка с масови съобщения
            $sql = "SELECT q.*, m.subject as mass_subject, m.content as mass_content
                    FROM `" . DB_PREFIX . "mail_queue` q
                    LEFT JOIN `" . DB_PREFIX . "mass_mailing_messages` m ON q.message_id = m.message_id
                    WHERE q.`status` = 'pending'
                    AND (q.`scheduled_at` IS NULL OR q.`scheduled_at` <= NOW())
                    AND q.`attempts` < q.`max_attempts`
                    ORDER BY q.`priority` ASC, q.`created_at` ASC
                    LIMIT " . (int)$limit;

            $query = $db->query($sql);
            $emails = $query->rows;

            foreach ($emails as $email) {
                $processed++;

                // Маркиране като processing
                self::updateQueueStatus($email['queue_id'], 'processing');

                try {
                    // Подготовка на опциите за изпращане
                    $options = [
                        'adaptor' => 'mail', // Може да се конфигурира
                        'from_email' => $email['from_email']
                    ];

                    // Определяме съдържанието на мейла
                    $messageContent = '';
                    $messageSubject = $email['subject'];

                    if (!empty($email['message_id']) && !empty($email['mass_content'])) {
                        // Използваме масовото съобщение
                        $messageContent = $email['mass_content'];
                        if (!empty($email['mass_subject'])) {
                            $messageSubject = $email['mass_subject'];
                        }
                    } else {
                        // Използваме директното съобщение (backward compatibility)
                        $messageContent = $email['message'];
                    }

                    $message = [
                        'to' => $email['to_email'],
                        'from' => $email['from_email'],
                        'sender' => $email['sender_name'],
                        'subject' => $messageSubject,
                        'html' => $messageContent
                    ];

                    // Изпращане на мейла
                    $result = self::sendQuick($options, $message, false);

                    if ($result) {
                        // Успешно изпращане
                        self::updateQueueStatus($email['queue_id'], 'sent', null, date('Y-m-d H:i:s'));
                        $sent++;

                        self::logDebug('Email sent from queue', [
                            'queue_id' => $email['queue_id'],
                            'to_email' => $email['to_email']
                        ]);
                    } else {
                        // Неуспешно изпращане
                        $attempts = $email['attempts'] + 1;
                        $status = ($attempts >= $email['max_attempts']) ? 'failed' : 'pending';
                        $errorMsg = 'Failed to send email (attempt ' . $attempts . ')';

                        self::updateQueueStatus($email['queue_id'], $status, $errorMsg, null, $attempts);
                        $failed++;
                        $errors[] = "Queue ID {$email['queue_id']}: {$errorMsg}";
                    }

                } catch (\Exception $e) {
                    // Грешка при изпращане
                    $attempts = $email['attempts'] + 1;
                    $status = ($attempts >= $email['max_attempts']) ? 'failed' : 'pending';
                    $errorMsg = 'Exception: ' . $e->getMessage();

                    self::updateQueueStatus($email['queue_id'], $status, $errorMsg, null, $attempts);
                    $failed++;
                    $errors[] = "Queue ID {$email['queue_id']}: {$errorMsg}";
                }

                // Забавяне между мейлите
                if ($processed < count($emails) && self::$queueConfig['delay_between_emails'] > 0) {
                    sleep(self::$queueConfig['delay_between_emails']);
                }
            }

        } catch (\Exception $e) {
            self::logDebug('Queue processing error', ['error' => $e->getMessage()]);
            $errors[] = 'Queue processing error: ' . $e->getMessage();
        }

        $result = [
            'processed' => $processed,
            'sent' => $sent,
            'failed' => $failed,
            'errors' => $errors
        ];

        self::logDebug('Queue processing completed', $result);
        return $result;
    }

    /**
     * Обновява статуса на мейл в опашката
     *
     * @param int $queueId Queue ID
     * @param string $status Нов статус
     * @param string $errorMessage Съобщение за грешка (опционално)
     * @param string $sentAt Време на изпращане (опционално)
     * @param int $attempts Брой опити (опционално)
     */
    protected static function updateQueueStatus(int $queueId, string $status, string $errorMessage = null, string $sentAt = null, int $attempts = null) {
        $db = self::getDatabase();

        $updates = ["status = '" . $db->escape($status) . "'"];

        if ($errorMessage !== null) {
            $updates[] = "error_message = '" . $db->escape($errorMessage) . "'";
        }

        if ($sentAt !== null) {
            $updates[] = "sent_at = '" . $db->escape($sentAt) . "'";
        }

        if ($attempts !== null) {
            $updates[] = "attempts = '" . (int)$attempts . "'";
        }

        $sql = "UPDATE `" . DB_PREFIX . "mail_queue`
                SET " . implode(', ', $updates) . "
                WHERE queue_id = '" . (int)$queueId . "'";

        $db->query($sql);
    }

    /**
     * Получава статистика за опашката
     *
     * @return array
     */
    public static function getQueueStats() {
        $db = self::getDatabase();

        $stats = [
            'pending' => 0,
            'processing' => 0,
            'sent' => 0,
            'failed' => 0,
            'total' => 0
        ];

        $sql = "SELECT status, COUNT(*) as count
                FROM `" . DB_PREFIX . "mail_queue`
                GROUP BY status";

        $query = $db->query($sql);

        foreach ($query->rows as $row) {
            $stats[$row['status']] = (int)$row['count'];
            $stats['total'] += (int)$row['count'];
        }

        return $stats;
    }

    /**
     * Изчиства стари записи от опашката
     *
     * @param int $daysOld Брой дни за запазване
     * @return int Брой изтрити записи
     */
    public static function cleanupQueue(int $daysOld = 30) {
        $db = self::getDatabase();

        // Първо намаляваме reference_count за масовите съобщения
        $sql = "SELECT DISTINCT `message_id` FROM `" . DB_PREFIX . "mail_queue`
                WHERE `message_id` IS NOT NULL
                AND status IN ('sent', 'failed')
                AND created_at < DATE_SUB(NOW(), INTERVAL " . (int)$daysOld . " DAY)";

        $result = $db->query($sql);
        $messageIds = [];

        if ($result->num_rows > 0) {
            foreach ($result->rows as $row) {
                $messageIds[] = $row['message_id'];
            }
        }

        // Изтриваме стари записи от queue
        $sql = "DELETE FROM `" . DB_PREFIX . "mail_queue`
                WHERE status IN ('sent', 'failed')
                AND created_at < DATE_SUB(NOW(), INTERVAL " . (int)$daysOld . " DAY)";

        $db->query($sql);
        $deletedCount = $db->countAffected();

        // Намаляваме reference_count за засегнатите масови съобщения
        foreach ($messageIds as $messageId) {
            self::decrementMassMessageReference($messageId);
        }

        // Изчистваме неизползвани масови съобщения
        $massDeleted = self::cleanupMassMessages($daysOld);

        self::logDebug('Queue cleanup completed', [
            'deleted_count' => $deletedCount,
            'mass_messages_deleted' => $massDeleted,
            'days' => $daysOld
        ]);

        return $deletedCount;
    }

    /**
     * Получава инстанция на базата данни
     *
     * @return object Database instance
     */
    protected static function getDatabase() {
        return CM()->getFirstDatabase();
    }

    protected static function getConfig($key, $default = null) {
        return CM()->getConfigFromFirstDB($key, $default);
    }

    protected static function sendWithCapture($mail) {
        $command = ini_get('sendmail_path');
        if (!$command) {
            return ['success' => false, 'error' => 'sendmail_path not configured'];
        }

        $descriptors = [
            0 => ['pipe', 'r'],
            1 => ['pipe', 'w'],
            2 => ['pipe', 'w']
        ];

        $process = proc_open($command, $descriptors, $pipes);
        if (!is_resource($process)) {
            return ['success' => false, 'error' => 'Cannot open sendmail process'];
        }

        // Генерираме mail съдържанието ръчно, тъй като Mail класът няма getContent() метод
        $mailContent = self::generateMailContent($mail);
        fwrite($pipes[0], $mailContent);
        fclose($pipes[0]);

        $stdout = stream_get_contents($pipes[1]);
        fclose($pipes[1]);

        $stderr = stream_get_contents($pipes[2]);
        fclose($pipes[2]);

        $status = proc_close($process);

        return [
            'success' => ($status === 0),
            'stdout' => $stdout,
            'stderr' => $stderr,
            'exit_code' => $status
        ];
    }

    /**
     * Генерира mail съдържание за sendmail
     *
     * @param \Mail $mail
     * @return string
     */
    protected static function generateMailContent($mail) {
        // Достъпваме protected свойствата чрез reflection
        $reflection = new \ReflectionClass($mail);

        $to = self::getMailProperty($reflection, $mail, 'to');
        $from = self::getMailProperty($reflection, $mail, 'from');
        $sender = self::getMailProperty($reflection, $mail, 'sender');
        $subject = self::getMailProperty($reflection, $mail, 'subject');
        $html = self::getMailProperty($reflection, $mail, 'html');
        $text = self::getMailProperty($reflection, $mail, 'text');
        $reply_to = self::getMailProperty($reflection, $mail, 'reply_to');

        if (is_array($to)) {
            $to = implode(',', $to);
        }

        $boundary = '----=_NextPart_' . md5(time());

        $content = '';
        $content .= 'To: ' . $to . PHP_EOL;
        $content .= 'Subject: =?UTF-8?B?' . base64_encode($subject) . '?=' . PHP_EOL;
        $content .= 'From: =?UTF-8?B?' . base64_encode($sender) . '?= <' . $from . '>' . PHP_EOL;

        if (!$reply_to) {
            $content .= 'Reply-To: =?UTF-8?B?' . base64_encode($sender) . '?= <' . $from . '>' . PHP_EOL;
        } else {
            $content .= 'Reply-To: =?UTF-8?B?' . base64_encode($reply_to) . '?= <' . $reply_to . '>' . PHP_EOL;
        }

        $content .= 'MIME-Version: 1.0' . PHP_EOL;
        $content .= 'Content-Type: multipart/alternative; boundary="' . $boundary . '"' . PHP_EOL;
        $content .= PHP_EOL;

        if ($text) {
            $content .= '--' . $boundary . PHP_EOL;
            $content .= 'Content-Type: text/plain; charset="utf-8"' . PHP_EOL;
            $content .= 'Content-Transfer-Encoding: 8bit' . PHP_EOL;
            $content .= PHP_EOL;
            $content .= $text . PHP_EOL;
        }

        if ($html) {
            $content .= '--' . $boundary . PHP_EOL;
            $content .= 'Content-Type: text/html; charset="utf-8"' . PHP_EOL;
            $content .= 'Content-Transfer-Encoding: 8bit' . PHP_EOL;
            $content .= PHP_EOL;
            $content .= $html . PHP_EOL;
        }

        $content .= '--' . $boundary . '--' . PHP_EOL;

        return $content;
    }

    /**
     * Получава protected свойство от Mail обекта чрез reflection
     *
     * @param \ReflectionClass $reflection
     * @param \Mail $mail
     * @param string $property
     * @return mixed
     */
    protected static function getMailProperty($reflection, $mail, $property) {
        try {
            $prop = $reflection->getProperty($property);
            $prop->setAccessible(true);
            return $prop->getValue($mail);
        } catch (\Exception $e) {
            return null;
        }
    }

    protected static function applySmtp($mail, $options) {
        foreach (['hostname', 'username', 'password', 'port', 'timeout'] as $field) {
            if (isset($options[$field]) && method_exists($mail, 'set' . ucfirst($field))) {
                $mail->{'set' . ucfirst($field)}($options[$field]);
            }
        }
        if (isset($options['parameter']) && method_exists($mail, 'setParameter')) {
            $mail->setParameter($options['parameter']);
        }
    }

    protected static function applyMail($mail, $options) {
        if (isset($options['parameter']) && method_exists($mail, 'setParameter')) {
            $mail->setParameter($options['parameter']);
        }
    }

    protected static function applyMessageDefaults($mail, $message) {
        foreach (['to', 'from', 'sender', 'subject', 'text', 'html'] as $field) {
            if (isset($message[$field]) && method_exists($mail, 'set' . ucfirst($field))) {
                $mail->{'set' . ucfirst($field)}($message[$field]);
            }
        }
        if (!empty($message['attachments'])) {
            foreach ($message['attachments'] as $file) {
                if (file_exists($file) && method_exists($mail, 'addAttachment')) {
                    $mail->addAttachment($file);
                } else {
                    self::logDebug('Attachment missing', $file);
                }
            }
        }
    }

    protected static function logDebug($message, $context = []) {
        if (self::$logLevel !== 'debug') return;
        $logDir = defined('DIR_LOGS') ? DIR_LOGS : __DIR__ . '/logs/';
        if (!is_dir($logDir)) @mkdir($logDir, 0777, true);
        $logFile = $logDir . 'mail_debug.log';
        if (file_exists($logFile) && filesize($logFile) > self::$maxLogSize) {
            rename($logFile, $logFile . '.' . date('Ymd_His') . '.bak');
        }
        $entry = '[' . date('Y-m-d H:i:s') . "] $message\n" . print_r(self::sanitizeForLog($context), true) . "\n";
        file_put_contents($logFile, $entry, FILE_APPEND);
        F()->log->developer($entry);
    }

    protected static function sanitizeForLog($data) {
        $replaceKeys = ['password', 'pass', 'token', 'api_key'];
        $out = $data;
        array_walk_recursive($out, function (&$value, $key) use ($replaceKeys) {
            if (in_array(strtolower($key), $replaceKeys)) {
                $value = '***';
            }
        });
        return $out;
    }

    public static function __callStatic($name, $arguments)
    {

        if(!self::$registry) self::$registry = CM()->getRegistry();

        if($name == 'db') return self::$registry->get('db');

        if(is_callable([self::$registry, $name])) {
            return call_user_func_array([self::$registry, $name], $arguments);
        }
    }

    /**
     * Получава пътя до lock файла
     *
     * @return string
     */
    protected static function getLockFilePath() {
        if (self::$lockFile === null) {
            $logDir = defined('DIR_LOGS') ? DIR_LOGS : __DIR__ . '/../../logs/';

            // Създаваме директорията ако не съществува
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            self::$lockFile = $logDir . 'mail_queue_processing.lock';
        }

        return self::$lockFile;
    }

    /**
     * Проверява дали обработката на опашката е в ход
     *
     * @return bool
     */
    public static function isProcessingLocked() {
        $lockFile = self::getLockFilePath();

        if (!file_exists($lockFile)) {
            return false;
        }

        // Проверяваме възрастта на lock файла (timeout 10 минути)
        $lockAge = time() - filemtime($lockFile);
        if ($lockAge > 600) { // 10 минути
            // Lock файлът е твърде стар, вероятно от прекъснат процес
            self::releaseLock();
            return false;
        }

        return true;
    }

    /**
     * Създава lock файл за обработка на опашката
     *
     * @return bool
     */
    protected static function acquireLock() {
        $lockFile = self::getLockFilePath();

        if (self::isProcessingLocked()) {
            return false; // Вече е заключено
        }

        $lockData = [
            'pid' => getmypid(),
            'started_at' => date('Y-m-d H:i:s'),
            'hostname' => gethostname()
        ];

        return file_put_contents($lockFile, json_encode($lockData, JSON_PRETTY_PRINT)) !== false;
    }

    /**
     * Премахва lock файла
     *
     * @return bool
     */
    protected static function releaseLock() {
        $lockFile = self::getLockFilePath();

        if (file_exists($lockFile)) {
            return unlink($lockFile);
        }

        return true;
    }

    /**
     * Стартира автоматична обработка на опашката ако не е в ход
     *
     * @return bool
     */
    public static function triggerAutoProcessing() {
        if (self::isProcessingLocked()) {
            return false; // Обработката вече е в ход
        }

        // Стартираме обработката в background
        try {
            self::processQueueWithLock();
            return true;
        } catch (\Exception $e) {
            self::logDebug('Auto processing failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Обработва опашката с lock механизъм
     *
     * @param int $limit Максимален брой мейли за обработка
     * @return array
     */
    protected static function processQueueWithLock(int $limit = null) {
        if (!self::acquireLock()) {
            return [
                'processed' => 0,
                'sent' => 0,
                'failed' => 0,
                'errors' => ['Could not acquire processing lock']
            ];
        }

        try {
            $result = self::processQueue($limit);
            return $result;
        } finally {
            self::releaseLock();
        }
    }

    /**
     * Създава масово съобщение за оптимизирано съхранение
     *
     * @param string $subject Тема на съобщението
     * @param string $content HTML съдържание
     * @return int|false Message ID или false при грешка
     */
    public static function createMassMessage(string $subject, string $content) {
        try {
            $db = self::getDatabase();

            $sql = "INSERT INTO `" . DB_PREFIX . "mass_mailing_messages`
                    SET `subject` = '" . $db->escape($subject) . "',
                        `content` = '" . $db->escape($content) . "',
                        `created_at` = NOW(),
                        `reference_count` = 0";

            $db->query($sql);
            $messageId = $db->getLastId();

            self::logDebug('Mass message created', [
                'message_id' => $messageId,
                'subject' => $subject
            ]);

            return $messageId;

        } catch (\Exception $e) {
            self::logDebug('Failed to create mass message', [
                'error' => $e->getMessage(),
                'subject' => $subject
            ]);
            return false;
        }
    }

    /**
     * Добавя мейл в опашката с референция към масово съобщение
     *
     * @param array $options Mail опции
     * @param array $message Съобщение данни
     * @param int $messageId ID на масовото съобщение
     * @param int $priority Приоритет
     * @param string $scheduledAt Планирано време
     * @return int|false Queue ID или false при грешка
     */
    public static function addToQueueWithMassMessage(array $options, array $message, int $messageId, int $priority = 1, string $scheduledAt = null) {
        try {
            self::initQueueConfig();
            $db = self::getDatabase();

            $queueData = [
                'to_email' => is_array($message['to']) ? implode(',', $message['to']) : $message['to'],
                'from_email' => $message['from'] ?? $options['from_email'] ?? '',
                'sender_name' => $message['sender'] ?? $options['sender'] ?? '',
                'subject' => $message['subject'] ?? '',
                'message_id' => $messageId,
                'priority' => max(1, min(5, $priority)),
                'created_at' => date('Y-m-d H:i:s'),
                'scheduled_at' => $scheduledAt ?? date('Y-m-d H:i:s'),
                'max_attempts' => self::$queueConfig['max_attempts']
            ];

            $sql = "INSERT INTO `" . DB_PREFIX . "mail_queue`
                    SET `to_email` = '" . $db->escape($queueData['to_email']) . "',
                        `from_email` = '" . $db->escape($queueData['from_email']) . "',
                        `sender_name` = '" . $db->escape($queueData['sender_name']) . "',
                        `subject` = '" . $db->escape($queueData['subject']) . "',
                        `message_id` = '" . (int)$queueData['message_id'] . "',
                        `priority` = '" . (int)$queueData['priority'] . "',
                        `created_at` = '" . $queueData['created_at'] . "',
                        `scheduled_at` = '" . $queueData['scheduled_at'] . "',
                        `max_attempts` = '" . (int)$queueData['max_attempts'] . "'";

            $db->query($sql);
            $queueId = $db->getLastId();

            // Увеличаваме reference_count на масовото съобщение
            $db->query("UPDATE `" . DB_PREFIX . "mass_mailing_messages`
                       SET `reference_count` = `reference_count` + 1
                       WHERE `message_id` = '" . (int)$messageId . "'");

            self::logDebug('Email added to queue with mass message', [
                'queue_id' => $queueId,
                'message_id' => $messageId,
                'to_email' => $queueData['to_email']
            ]);

            // Стартираме автоматична обработка ако не е в ход
            self::triggerAutoProcessing();

            return $queueId;

        } catch (\Exception $e) {
            self::logDebug('Failed to add email to queue with mass message', [
                'error' => $e->getMessage(),
                'message_id' => $messageId
            ]);
            return false;
        }
    }

    /**
     * Изчиства неизползвани масови съобщения
     *
     * @param int $days Дни за запазване
     * @return int Брой изтрити записи
     */
    public static function cleanupMassMessages(int $days = 30) {
        try {
            $db = self::getDatabase();

            // Изтриваме масови съобщения без референции и по-стари от X дни
            $sql = "DELETE FROM `" . DB_PREFIX . "mass_mailing_messages`
                    WHERE `reference_count` = 0
                    AND `created_at` < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)";

            $result = $db->query($sql);
            $deletedCount = $db->countAffected();

            self::logDebug('Mass messages cleanup completed', [
                'deleted_count' => $deletedCount,
                'days' => $days
            ]);

            return $deletedCount;

        } catch (\Exception $e) {
            self::logDebug('Mass messages cleanup failed', [
                'error' => $e->getMessage(),
                'days' => $days
            ]);
            return 0;
        }
    }

    /**
     * Намалява reference_count на масово съобщение при изтриване на queue запис
     *
     * @param int $messageId ID на масовото съобщение
     * @return bool
     */
    public static function decrementMassMessageReference(int $messageId) {
        try {
            $db = self::getDatabase();

            $sql = "UPDATE `" . DB_PREFIX . "mass_mailing_messages`
                    SET `reference_count` = GREATEST(0, `reference_count` - 1)
                    WHERE `message_id` = '" . (int)$messageId . "'";

            $db->query($sql);

            return true;

        } catch (\Exception $e) {
            self::logDebug('Failed to decrement mass message reference', [
                'error' => $e->getMessage(),
                'message_id' => $messageId
            ]);
            return false;
        }
    }

}
