/**
 * JavaScript модул за преглед на поръчка
 * Разширява основния BackendModule клас
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за преглед на поръчка
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initOrderInfoModule();
        } else {
            console.error('ORDER-INFO.JS: BackendModule is not defined!');
        }
    });

    // Добавяне на функционалност за преглед на поръчка към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || '';
        } catch (e) {
            console.error('Error parsing URL params for user_token:', e);
            BackendModule.config.userToken = '';
        }

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за преглед на поръчка
             */
            initOrderInfoModule: function() {
                this.initOrderStatusUpdate();
                this.initOrderNotifications();
                this.initOrderDetailModal();
            },

            /**
             * Инициализация на функционалността за промяна на статус на поръчка
             */
            initOrderStatusUpdate: function() {
                const statusUpdateForm = document.getElementById('status-update-form');
                
                if (statusUpdateForm) {
                    statusUpdateForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        const formData = new FormData(this);

                        const actionUrl = this.action + '&user_token=' + BackendModule.config.userToken;
                        
                        fetch(actionUrl, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                BackendModule.showOrderAlert('success', data.success);
                                // Презареждане на страницата след 2 секунди
                                setTimeout(() => {
                                    window.location.reload();
                                }, 2000);
                            } else if (data.error) {
                                BackendModule.showOrderAlert('error', data.error);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            BackendModule.showOrderAlert('error', 'Възникна грешка при актуализиране на статуса');
                        });
                    });
                }
                
                // Бутони за бърза промяна на статус в модала за детайли
                const statusButtons = document.querySelectorAll('[data-status-id]');
                const commentInput = document.querySelector('input[name="comment"]');
                const notifyCheckbox = document.querySelector('input[name="notify"]');
                statusButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const statusId = this.getAttribute('data-status-id');
                        const orderId = this.getAttribute('data-order-id');
                        if (statusId && orderId) {
                            BackendModule.updateOrderStatus(orderId, statusId, commentInput.value, notifyCheckbox.checked);
                        }
                    });
                });
            },

            /**
             * Актуализиране на статус на поръчка
             */
            updateOrderStatus: function(orderId, statusId, comment = '', notify = false) {
                const formData = new FormData();
                formData.append('order_id', orderId);
                formData.append('order_status_id', statusId);
                formData.append('comment', comment);
                formData.append('notify', notify ? '1' : '0');

                console.log('Updating order status:', orderId, statusId, comment, notify);

                const updateUrl = window.location.origin + window.location.pathname + '?route=sale/order/updateStatus&user_token=' + BackendModule.config.userToken;
                
                fetch(updateUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        BackendModule.showOrderAlert('success', data.success);
                        // Актуализиране на статуса в таблицата без презареждане
                        BackendModule.updateOrderStatusInTable(orderId, statusId);
                    } else if (data.error) {
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    BackendModule.showOrderAlert('error', 'Възникна грешка при актуализиране на статуса');
                });
            },

            /**
             * Актуализиране на статуса в таблицата
             */
            updateOrderStatusInTable: function(orderId, statusId) {
                const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
                if (orderRow) {
                    const statusCell = orderRow.querySelector('.status-badge');
                    if (statusCell) {
                        // Тук трябва да се добави логика за актуализиране на статуса
                        // в зависимост от statusId
                        statusCell.textContent = 'Актуализиран';
                    }
                }
            },

            /**
             * Инициализация на функционалността за известия
             */
            initOrderNotifications: function() {
                const notificationForm = document.querySelector('[data-notification-form]');
                
                if (notificationForm) {
                    const sendButton = notificationForm.querySelector('[data-send-notification]');
                    
                    if (sendButton) {
                        sendButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            const subject = notificationForm.querySelector('[name="subject"]').value;
                            const message = notificationForm.querySelector('[name="message"]').value;
                            const orderId = notificationForm.querySelector('[name="order_id"]').value;
                            
                            if (!subject.trim() || !message.trim()) {
                                BackendModule.showOrderAlert('error', 'Моля, попълнете всички полета');
                                return;
                            }
                            
                            BackendModule.sendOrderNotification(orderId, subject, message);
                        });
                    }
                }
            },

            /**
             * Изпращане на известие за поръчка
             */
            sendOrderNotification: function(orderId, subject, message) {
                const formData = new FormData();
                formData.append('order_id', orderId);
                formData.append('subject', subject);
                formData.append('message', message);
                
                const notificationUrl = window.location.origin + window.location.pathname + '?route=sale/order/sendNotification&user_token=' + BackendModule.config.userToken;
                
                fetch(notificationUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        BackendModule.showOrderAlert('success', data.success);
                        // Изчистване на формата
                        const form = document.querySelector('[data-notification-form]');
                        if (form) {
                            form.querySelector('[name="subject"]').value = '';
                            form.querySelector('[name="message"]').value = '';
                        }
                    } else if (data.error) {
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    BackendModule.showOrderAlert('error', 'Възникна грешка при изпращане на известието');
                });
            },

            /**
             * Инициализация на Order Detail Modal
             */
            initOrderDetailModal: function() {
                const showModalBtn = document.getElementById('show-order-detail');
                const modal = document.getElementById('order-detail-modal');
                const closeModalBtn = document.getElementById('close-order-detail');

                if (showModalBtn && modal) {
                    // Показване на модала
                    showModalBtn.addEventListener('click', function() {
                        console.log('Opening order detail modal');
                        modal.classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                    });

                    // Затваряне на модала
                    if (closeModalBtn) {
                        closeModalBtn.addEventListener('click', function() {
                            console.log('Closing order detail modal');
                            modal.classList.add('hidden');
                            document.body.style.overflow = 'auto';
                        });
                    }

                    // Затваряне при клик извън модала
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            console.log('Closing modal by clicking outside');
                            modal.classList.add('hidden');
                            document.body.style.overflow = 'auto';
                        }
                    });
                }
            },

            /**
             * Показване на съобщение специфично за поръчки
             */
            showOrderAlert: function(type, message, duration = 3) {
                // Използваме същата функция като в основния модул, но с префикс за поръчки
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message, duration);
                } else {
                    // Fallback ако основната функция не е налична
                    alert(message);
                }
            }
        });
    }

})();
