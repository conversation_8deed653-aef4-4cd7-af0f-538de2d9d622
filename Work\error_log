[19-Aug-2025 18:52:57 Europe/Sofia] PHP Fatal error:  Uncaught Error: Class 'Theme25\Backend\Controller\Sale\Order\MailHelper' not found in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php:200
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php(158): Theme25\Backend\Controller\Sale\Order\StatusUpdate->sendEmail('web@ivan-avramo...', '\xD0\x90\xD0\xBA\xD1\x82\xD1\x83\xD0\xB0\xD0\xBB\xD0\xB8\xD0...', '\xD0\x97\xD0\xB4\xD1\x80\xD0\xB0\xD0\xB2\xD0\xB5\xD0\xB9\xD1...')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php(120): Theme25\Backend\Controller\Sale\Order\StatusUpdate->sendStatusUpdateNotification(50199, 8, '\xD1\x82\xD0\xB5\xD1\x81\xD1\x82')
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php(37): Theme25\Backend\Controller\Sale\Order\StatusUpdate->processStatusUpdate(50199, 8, '\xD1\x82\xD0\xB5\xD1\x81\xD1\x82', true)
#3 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order.php(149): Theme25\Backend\Controlle in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php on line 200
[19-Aug-2025 18:54:09 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function get() on null in /home/<USER>/storage_theme25/theme/Helper/MailHelper.php:491
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Helper/MailHelper.php(41): Theme25\Helper\MailHelper::getConfig('config_mail_eng...', 'mail')
#1 /home/<USER>/storage_theme25/theme/Helper/MailHelper.php(60): Theme25\Helper\MailHelper::getMailOptions()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php(200): Theme25\Helper\MailHelper::getMailOption('from_email')
#3 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php(158): Theme25\Backend\Controller\Sale\Order\StatusUpdate->sendEmail('web@ivan-avramo...', '\xD0\x90\xD0\xBA\xD1\x82\xD1\x83\xD0\xB0\xD0\xBB\xD0\xB8\xD0...', '\xD0\x97\xD0\xB4\xD1\x80\xD0\xB0\xD0\xB2\xD0\xB5\xD0\xB9\xD1...')
#4 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/StatusUpdate.php(120): Theme25\Backend\Controller\Sale\Order\StatusUpdate->sendStatusUpdateNotification(50199, 7, '\xD1\x82\xD0\xB5\x in /home/<USER>/storage_theme25/theme/Helper/MailHelper.php on line 491
[19-Aug-2025 19:04:58 Europe/Sofia] Order #50199 status changed to 8 by user unknown. Comment: тест
