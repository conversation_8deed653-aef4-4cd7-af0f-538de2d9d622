/**
 * JavaScript модул за редактиране на поръчка
 * Разширява основния BackendModule клас
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за редактиране на поръчка
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initOrderEditModule();
        } else {
            console.error('ORDER-EDIT.JS: BackendModule is not defined!');
        }
    });

    // Добавяне на функционалност за редактиране на поръчка към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || '';
        } catch (e) {
            console.error('Error parsing URL params for user_token:', e);
            BackendModule.config.userToken = '';
        }

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за редактиране на поръчка
             */
            initOrderEditModule: function() {
                this.initOrderEditForm();
                this.initOrderValidation();
                this.initAddressHandling();
                this.initProductManagement();
                this.initOrderTotalsCalculation();
            },

            /**
             * Инициализация на формата за редактиране на поръчка
             */
            initOrderEditForm: function() {
                const orderForm = document.getElementById('order-edit-form');
                
                if (orderForm) {
                    orderForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // Валидация на формата
                        if (!BackendModule.validateOrderForm(this)) {
                            return;
                        }
                        
                        const formData = new FormData(this);
                        const userToken = BackendModule.config.userToken;
                        
                        if (userToken) {
                            formData.append('user_token', userToken);
                        }
                        
                        // Показване на loading индикатор
                        BackendModule.showLoadingIndicator();
                        
                        fetch(this.action, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            BackendModule.hideLoadingIndicator();
                            
                            if (data.success) {
                                BackendModule.showOrderAlert('success', data.success);
                                // Пренасочване към страницата с информация за поръчката
                                setTimeout(() => {
                                    const orderId = document.querySelector('[name="order_id"]').value;
                                    const infoUrl = window.location.origin + window.location.pathname + 
                                                  '?route=sale/order/info&order_id=' + orderId + 
                                                  '&user_token=' + BackendModule.config.userToken;
                                    window.location.href = infoUrl;
                                }, 1500);
                            } else if (data.error) {
                                BackendModule.showOrderAlert('error', data.error);
                            }
                        })
                        .catch(error => {
                            BackendModule.hideLoadingIndicator();
                            console.error('Error:', error);
                            BackendModule.showOrderAlert('error', 'Възникна грешка при запазване на поръчката');
                        });
                    });
                }
            },

            /**
             * Инициализация на валидацията на формата
             */
            initOrderValidation: function() {
                // Валидация в реalno време
                const requiredFields = document.querySelectorAll('[required]');
                
                requiredFields.forEach(field => {
                    field.addEventListener('blur', function() {
                        BackendModule.validateField(this);
                    });
                    
                    field.addEventListener('input', function() {
                        // Премахване на грешката при въвеждане
                        this.classList.remove('border-red-500');
                        const errorMsg = this.parentNode.querySelector('.error-message');
                        if (errorMsg) {
                            errorMsg.remove();
                        }
                    });
                });
                
                // Валидация на имейл полета
                const emailFields = document.querySelectorAll('input[type="email"]');
                emailFields.forEach(field => {
                    field.addEventListener('blur', function() {
                        BackendModule.validateEmailField(this);
                    });
                });
            },

            /**
             * Валидация на цялата форма
             */
            validateOrderForm: function(form) {
                let isValid = true;
                const requiredFields = form.querySelectorAll('[required]');
                
                // Изчистване на предишни грешки
                form.querySelectorAll('.error-message').forEach(msg => msg.remove());
                form.querySelectorAll('.border-red-500').forEach(field => {
                    field.classList.remove('border-red-500');
                });
                
                requiredFields.forEach(field => {
                    if (!BackendModule.validateField(field)) {
                        isValid = false;
                    }
                });
                
                // Валидация на имейл полета
                const emailFields = form.querySelectorAll('input[type="email"]');
                emailFields.forEach(field => {
                    if (!BackendModule.validateEmailField(field)) {
                        isValid = false;
                    }
                });
                
                return isValid;
            },

            /**
             * Валидация на отделно поле
             */
            validateField: function(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';
                
                if (field.hasAttribute('required') && !value) {
                    isValid = false;
                    errorMessage = 'Това поле е задължително';
                }
                
                if (!isValid) {
                    field.classList.add('border-red-500');
                    BackendModule.showFieldError(field, errorMessage);
                } else {
                    field.classList.remove('border-red-500');
                    BackendModule.hideFieldError(field);
                }
                
                return isValid;
            },

            /**
             * Валидация на имейл поле
             */
            validateEmailField: function(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';
                
                if (value && !BackendModule.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Моля, въведете валиден имейл адрес';
                }
                
                if (!isValid) {
                    field.classList.add('border-red-500');
                    BackendModule.showFieldError(field, errorMessage);
                } else {
                    field.classList.remove('border-red-500');
                    BackendModule.hideFieldError(field);
                }
                
                return isValid;
            },

            /**
             * Проверка за валиден имейл
             */
            isValidEmail: function(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            },

            /**
             * Показване на грешка за поле
             */
            showFieldError: function(field, message) {
                // Премахване на съществуваща грешка
                this.hideFieldError(field);
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                errorDiv.textContent = message;
                
                field.parentNode.appendChild(errorDiv);
            },

            /**
             * Скриване на грешка за поле
             */
            hideFieldError: function(field) {
                const errorMsg = field.parentNode.querySelector('.error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }
            },

            /**
             * Инициализация на работата с адреси
             */
            initAddressHandling: function() {
                // Копиране на адрес за плащане към адрес за доставка
                const copyAddressBtn = document.getElementById('copy-billing-address');
                
                if (copyAddressBtn) {
                    copyAddressBtn.addEventListener('click', function() {
                        BackendModule.copyBillingToShipping();
                    });
                }
                
                // Автоматично попълване на адреси при избор на клиент
                const customerSelect = document.getElementById('customer-select');
                if (customerSelect) {
                    customerSelect.addEventListener('change', function() {
                        const customerId = this.value;
                        if (customerId) {
                            BackendModule.loadCustomerAddresses(customerId);
                        }
                    });
                }
            },

            /**
             * Копиране на адрес за плащане към адрес за доставка
             */
            copyBillingToShipping: function() {
                const billingFields = [
                    'payment_firstname', 'payment_lastname', 'payment_company',
                    'payment_address_1', 'payment_address_2', 'payment_city',
                    'payment_postcode', 'payment_country_id', 'payment_zone_id'
                ];
                
                const shippingFields = [
                    'shipping_firstname', 'shipping_lastname', 'shipping_company',
                    'shipping_address_1', 'shipping_address_2', 'shipping_city',
                    'shipping_postcode', 'shipping_country_id', 'shipping_zone_id'
                ];
                
                billingFields.forEach((billingField, index) => {
                    const billingInput = document.querySelector(`[name="${billingField}"]`);
                    const shippingInput = document.querySelector(`[name="${shippingFields[index]}"]`);
                    
                    if (billingInput && shippingInput) {
                        shippingInput.value = billingInput.value;
                    }
                });
                
                BackendModule.showOrderAlert('success', 'Адресът за плащане е копиран към адреса за доставка');
            },

            /**
             * Зареждане на адресите на клиент
             */
            loadCustomerAddresses: function(customerId) {
                const formData = new FormData();
                formData.append('customer_id', customerId);
                formData.append('user_token', BackendModule.config.userToken);
                
                const addressUrl = window.location.origin + window.location.pathname + '?route=sale/order/getCustomerAddresses';
                
                fetch(addressUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.addresses) {
                        BackendModule.populateAddressFields(data.addresses);
                    }
                })
                .catch(error => {
                    console.error('Error loading customer addresses:', error);
                });
            },

            /**
             * Попълване на полетата за адреси
             */
            populateAddressFields: function(addresses) {
                if (addresses.billing) {
                    Object.keys(addresses.billing).forEach(key => {
                        const field = document.querySelector(`[name="payment_${key}"]`);
                        if (field) {
                            field.value = addresses.billing[key];
                        }
                    });
                }
                
                if (addresses.shipping) {
                    Object.keys(addresses.shipping).forEach(key => {
                        const field = document.querySelector(`[name="shipping_${key}"]`);
                        if (field) {
                            field.value = addresses.shipping[key];
                        }
                    });
                }
            },

            /**
             * Показване на loading индикатор
             */
            showLoadingIndicator: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }
            },

            /**
             * Скриване на loading индикатор
             */
            hideLoadingIndicator: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="ri-save-line mr-2"></i>Запази промените';
                }
            },

            /**
             * Инициализация на управлението на продукти
             */
            initProductManagement: function() {
                // Добавяне на нов продукт
                const addProductBtn = document.getElementById('add-product-btn');
                if (addProductBtn) {
                    addProductBtn.addEventListener('click', function() {
                        BackendModule.showAddProductModal();
                    });
                }

                // Премахване на продукт
                const removeProductBtns = document.querySelectorAll('.remove-product-btn');
                removeProductBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const productRow = this.closest('tr');
                        if (productRow && confirm('Сигурни ли сте, че искате да премахнете този продукт?')) {
                            productRow.remove();
                            BackendModule.calculateOrderTotals();
                        }
                    });
                });

                // Промяна на количество
                const quantityInputs = document.querySelectorAll('.product-quantity');
                quantityInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.calculateOrderTotals();
                    });
                });

                // Промяна на цена
                const priceInputs = document.querySelectorAll('.product-price');
                priceInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.calculateOrderTotals();
                    });
                });
            },

            /**
             * Показване на модал за добавяне на продукт
             */
            showAddProductModal: function() {
                // Създаване на модален прозорец
                const modal = document.createElement('div');
                modal.id = 'add-product-modal';
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg w-full max-w-lg mx-4">
                        <div class="flex justify-between items-center p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">Добавяне на продукт</h3>
                            <button class="close-modal text-gray-400 hover:text-gray-500">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            <form id="add-product-form">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Продукт</label>
                                    <input type="text" id="product-search" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" placeholder="Търсене на продукт...">
                                    <div id="product-suggestions" class="hidden absolute z-10 w-full bg-white border border-gray-300 rounded-button mt-1 max-h-48 overflow-y-auto"></div>
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Количество</label>
                                    <input type="number" name="quantity" min="1" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Цена</label>
                                    <input type="number" name="price" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                                </div>
                                <div class="flex justify-end space-x-2">
                                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Отказ</button>
                                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90">Добави продукт</button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;

                // Добавяне на event listeners
                modal.addEventListener('click', (e) => {
                    if (e.target === modal || e.target.classList.contains('close-modal')) {
                        document.body.removeChild(modal);
                        document.body.style.overflow = 'auto';
                    }
                });

                // Обработка на формата
                modal.querySelector('#add-product-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    BackendModule.addProductToOrder(new FormData(e.target));
                    document.body.removeChild(modal);
                    document.body.style.overflow = 'auto';
                });

                // Търсене на продукти
                const productSearch = modal.querySelector('#product-search');
                productSearch.addEventListener('input', function() {
                    BackendModule.searchProducts(this.value);
                });

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';
            },

            /**
             * Търсене на продукти
             */
            searchProducts: function(query) {
                if (query.length < 2) {
                    document.getElementById('product-suggestions').classList.add('hidden');
                    return;
                }

                const formData = new FormData();
                formData.append('search', query);
                formData.append('user_token', BackendModule.config.userToken);

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts';

                fetch(searchUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.displayProductSuggestions(data.products || []);
                })
                .catch(error => {
                    console.error('Error searching products:', error);
                });
            },

            /**
             * Показване на предложения за продукти
             */
            displayProductSuggestions: function(products) {
                const suggestionsDiv = document.getElementById('product-suggestions');

                if (products.length === 0) {
                    suggestionsDiv.classList.add('hidden');
                    return;
                }

                suggestionsDiv.innerHTML = '';
                products.forEach(product => {
                    const suggestion = document.createElement('div');
                    suggestion.className = 'p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-200';
                    suggestion.innerHTML = `
                        <div class="font-medium">${product.name}</div>
                        <div class="text-sm text-gray-500">Модел: ${product.model} | Цена: ${product.price}</div>
                    `;

                    suggestion.addEventListener('click', function() {
                        document.getElementById('product-search').value = product.name;
                        document.querySelector('[name="price"]').value = product.price;
                        document.querySelector('[name="product_id"]').value = product.product_id;
                        suggestionsDiv.classList.add('hidden');
                    });

                    suggestionsDiv.appendChild(suggestion);
                });

                suggestionsDiv.classList.remove('hidden');
            },

            /**
             * Добавяне на продукт към поръчката
             */
            addProductToOrder: function(formData) {
                const productName = document.getElementById('product-search').value;
                const quantity = formData.get('quantity');
                const price = formData.get('price');
                const productId = document.querySelector('[name="product_id"]').value || 0;

                if (!productName || !quantity || !price) {
                    BackendModule.showOrderAlert('error', 'Моля, попълнете всички полета');
                    return;
                }

                // Добавяне на ред в таблицата с продукти
                const productsTable = document.getElementById('order-products-table');
                const tbody = productsTable.querySelector('tbody');

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-2 border-b">${productName}</td>
                    <td class="px-4 py-2 border-b">
                        <input type="number" class="product-quantity w-20 px-2 py-1 border rounded" value="${quantity}" min="1">
                    </td>
                    <td class="px-4 py-2 border-b">
                        <input type="number" class="product-price w-24 px-2 py-1 border rounded" value="${price}" step="0.01" min="0">
                    </td>
                    <td class="px-4 py-2 border-b product-total">${(quantity * price).toFixed(2)}</td>
                    <td class="px-4 py-2 border-b">
                        <button type="button" class="remove-product-btn text-red-500 hover:text-red-700">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </td>
                    <input type="hidden" name="products[${Date.now()}][product_id]" value="${productId}">
                    <input type="hidden" name="products[${Date.now()}][name]" value="${productName}">
                `;

                tbody.appendChild(row);

                // Добавяне на event listeners за новия ред
                const quantityInput = row.querySelector('.product-quantity');
                const priceInput = row.querySelector('.product-price');
                const removeBtn = row.querySelector('.remove-product-btn');

                quantityInput.addEventListener('change', function() {
                    BackendModule.updateProductTotal(this);
                    BackendModule.calculateOrderTotals();
                });

                priceInput.addEventListener('change', function() {
                    BackendModule.updateProductTotal(this);
                    BackendModule.calculateOrderTotals();
                });

                removeBtn.addEventListener('click', function() {
                    if (confirm('Сигурни ли сте, че искате да премахнете този продукт?')) {
                        row.remove();
                        BackendModule.calculateOrderTotals();
                    }
                });

                BackendModule.calculateOrderTotals();
                BackendModule.showOrderAlert('success', 'Продуктът е добавен успешно');
            },

            /**
             * Актуализиране на общата сума за продукт
             */
            updateProductTotal: function(input) {
                const row = input.closest('tr');
                const quantity = parseFloat(row.querySelector('.product-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.product-price').value) || 0;
                const total = quantity * price;

                row.querySelector('.product-total').textContent = total.toFixed(2);
            },

            /**
             * Инициализация на изчисляването на общи суми
             */
            initOrderTotalsCalculation: function() {
                // Автоматично изчисляване при промяна на данни
                const form = document.getElementById('order-edit-form');
                if (form) {
                    form.addEventListener('change', function(e) {
                        if (e.target.classList.contains('product-quantity') ||
                            e.target.classList.contains('product-price')) {
                            BackendModule.calculateOrderTotals();
                        }
                    });
                }
            },

            /**
             * Изчисляване на общите суми на поръчката
             */
            calculateOrderTotals: function() {
                let subtotal = 0;

                // Изчисляване на подобща сума
                const productTotals = document.querySelectorAll('.product-total');
                productTotals.forEach(totalCell => {
                    subtotal += parseFloat(totalCell.textContent) || 0;
                });

                // Актуализиране на полетата за суми
                const subtotalField = document.getElementById('order-subtotal');
                if (subtotalField) {
                    subtotalField.textContent = subtotal.toFixed(2);
                }

                // Изчисляване на общата сума (може да включва данъци, доставка и т.н.)
                const shippingCost = parseFloat(document.getElementById('shipping-cost')?.value) || 0;
                const taxRate = parseFloat(document.getElementById('tax-rate')?.value) || 0;

                const tax = subtotal * (taxRate / 100);
                const total = subtotal + shippingCost + tax;

                const totalField = document.getElementById('order-total');
                if (totalField) {
                    totalField.textContent = total.toFixed(2);
                }

                const taxField = document.getElementById('order-tax');
                if (taxField) {
                    taxField.textContent = tax.toFixed(2);
                }
            },

            /**
             * Показване на съобщение специфично за поръчки
             */
            showOrderAlert: function(type, message, duration = 3) {
                // Използваме същата функция като в основния модул, но с префикс за поръчки
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message, duration);
                } else {
                    // Fallback ако основната функция не е налична
                    alert(message);
                }
            }
        });
    }

})();
