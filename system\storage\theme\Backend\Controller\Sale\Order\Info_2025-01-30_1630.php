<?php

namespace Theme25\Backend\Controller\Sale\Order;

class Info extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'order-info.js',
        ]);
    }

    public function prepareOrderInfo() {
        $order_id = (int)$this->requestGet('order_id');
        
        if (!$order_id) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return;
        }

        $this->prepareOrderData($order_id)
             ->prepareOrderProducts($order_id)
             ->prepareOrderTotals($order_id)
             ->prepareOrderHistory($order_id)
             ->prepareOrderStatuses();

        $this->setData([
            'back_url' => $this->getAdminLink('sale/order'),
            'order_id' => $order_id
        ]);
    }

    /**
     * Подготвя основните данни за поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderData($order_id) {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/order' => 'orders'
        ]);


        // Получаване на информацията за поръчката
        $order_info = $this->orders->getOrder($order_id);

        if (!$order_info) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return $this;
        }

        // Подготовка на данните за поръчката
        $order_status_id = $order_info['order_status_id'];

        $order_data = [
            'order_id' => $order_info['order_id'],
            'customer' => $order_info['customer'],
            'customer_id' => $order_info['customer_id'],
            'email' => $order_info['email'],
            'telephone' => $order_info['telephone'],
            'order_status' => $order_info['order_status'],
            'order_status_id' => $order_status_id,
            'status_css_class' => $this->getStatusClass($order_status_id),
            'payment_method' => $order_info['payment_method'],
            'payment_code' => $order_info['payment_code'],
            'shipping_method' => $order_info['shipping_method'],
            'shipping_code' => $order_info['shipping_code'],
            'total' => $this->formatCurrency($order_info['total'], $order_info['currency_code'], $order_info['currency_value']),
            'currency_code' => $order_info['currency_code'],
            'currency_value' => $order_info['currency_value'],
            'date_added' => $this->formatDate($order_info['date_added']),
            'date_modified' => $this->formatDate($order_info['date_modified']),
            'comment' => $order_info['comment'],
            
            // Адрес за плащане
            'payment_firstname' => $order_info['payment_firstname'],
            'payment_lastname' => $order_info['payment_lastname'],
            'payment_company' => $order_info['payment_company'],
            'payment_address_1' => $order_info['payment_address_1'],
            'payment_address_2' => $order_info['payment_address_2'],
            'payment_city' => $order_info['payment_city'],
            'payment_postcode' => $order_info['payment_postcode'],
            'payment_country' => $order_info['payment_country'],
            'payment_zone' => $order_info['payment_zone'],
            
            // Адрес за доставка
            'shipping_firstname' => $order_info['shipping_firstname'],
            'shipping_lastname' => $order_info['shipping_lastname'],
            'shipping_company' => $order_info['shipping_company'],
            'shipping_address_1' => $order_info['shipping_address_1'],
            'shipping_address_2' => $order_info['shipping_address_2'],
            'shipping_city' => $order_info['shipping_city'],
            'shipping_postcode' => $order_info['shipping_postcode'],
            'shipping_country' => $order_info['shipping_country'],
            'shipping_zone' => $order_info['shipping_zone']
        ];

        // URL адреси
        $routes = [
            'edit_url' => 'sale/order/edit&order_id=' . $order_id,
            'invoice_url' => 'sale/order/invoice&order_id=' . $order_id,
            'shipping_url' => 'sale/order/shipping&order_id=' . $order_id,
            'update_status_url' => 'sale/order/updateStatus'
        ];

        $this->setData($order_data);
        $this->setData($this->getAdminLinks($routes));

        return $this;
    }

    /**
     * Подготвя продуктите в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderProducts($order_id) {
        // Получаване на продуктите в поръчката
        $order_products = $this->orders->getOrderProducts($order_id);
        
        $products = [];
        
        foreach ($order_products as $product) {
            // Получаване на опциите за продукта
            $options = $this->orders->getOrderOptions($order_id, $product['order_product_id']);
            
            $product_options = [];
            foreach ($options as $option) {
                $product_options[] = [
                    'name' => $option['name'],
                    'value' => $option['value']
                ];
            }
            
            $products[] = [
                'order_product_id' => $product['order_product_id'],
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'quantity' => $product['quantity'],
                'price' => $this->formatCurrency($product['price'], $this->getData('currency_code'), $this->getData('currency_value')),
                'total' => $this->formatCurrency($product['total'], $this->getData('currency_code'), $this->getData('currency_value')),
                'options' => $product_options
            ];
        }

        $this->setData('order_products', $products);

        return $this;
    }

    /**
     * Подготвя общите суми на поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderTotals($order_id) {
        // Получаване на общите суми
        $order_totals = $this->orders->getOrderTotals($order_id);
        
        $totals = [];
        
        foreach ($order_totals as $total) {
            $totals[] = [
                'title' => $total['title'],
                'text' => $this->formatCurrency($total['value'], $this->getData('currency_code'), $this->getData('currency_value')),
                'value' => $total['value'],
                'code' => $total['code'],
                'sort_order' => $total['sort_order']
            ];
        }

        $this->setData('order_totals', $totals);

        return $this;
    }

    /**
     * Подготвя историята на поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderHistory($order_id) {
        // Получаване на историята на поръчката
        $order_histories = $this->orders->getOrderHistories($order_id);
        
        $histories = [];
        
        foreach ($order_histories as $history) {
            $histories[] = [
                'status' => $history['status'],
                'comment' => $history['comment'],
                'notify' => $history['notify'],
                'date_added' => $this->formatDate($history['date_added'])
            ];
        }

        $this->setData('order_histories', $histories);

        return $this;
    }

    /**
     * Подготвя статусите на поръчки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderStatuses() {
        // Зареждане на модела за статуси
        $this->loadModelAs('localisation/order_status', 'orderStatuses');
        
        // Получаване на всички статуси
        $order_statuses = $this->orderStatuses->getOrderStatuses();
        
        $statuses = [];
        foreach ($order_statuses as $status) {
            $statuses[] = [
                'order_status_id' => $status['order_status_id'],
                'name' => $status['name']
            ];
        }

        $this->setData('order_statuses', $statuses);

        return $this;
    }

    /**
     * Получава статус класа за визуализация
     *
     * @param int $status_id ID на статуса
     * @return string CSS клас за статуса
     */
    private function getStatusClass($status_id) {
        $status_classes = [
            1 => 'status-new',      // Нова
            2 => 'status-processing', // В процес
            3 => 'status-sent', // Изпратена
            5 => 'status-completed',  // Изпълнена
            7 => 'status-cancelled',  // Отказана
            8 => 'status-rejected',  // Отхвърлена
            10 => 'status-failed',  // Провалена
            11 => 'status-returned-payment', // Върнато плащане
            12 => 'status-returned', // Върната
            15 => 'status-processed', // Обработена
            16 => 'status-reset', // Нулирана
            17 => 'status-card-payed', // Платена с карта
            18 => 'status-card-rejected', // Отхвърлено плащане с карта
            19 => 'status-delayed', // В изчакване за по-късна дата
        ];

        $class = $status_classes[$status_id] ?? 'status-unknown';
        return ' ' . $class; // Добавяме интервал в началото
    }
}
