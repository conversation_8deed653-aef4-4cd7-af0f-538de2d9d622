<!-- Order Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Редакция на поръчка #{{ order_id }}</h1>
            <p class="text-gray-500 mt-1">Редактиране на информацията за поръчката</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2">
            {% if back_url %}
            <a href="{{ back_url }}" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-arrow-left-line"></i>
                </div>
                <span>Назад</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <form method="post" action="{{ save_url }}" class="max-w-7xl space-y-6">
        <input type="hidden" name="user_token" value="{{ user_token }}">
        
        <!-- Customer Information -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Информация за клиента</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Име *</label>
                        <input type="text" name="firstname" value="{{ firstname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фамилия *</label>
                        <input type="text" name="lastname" value="{{ lastname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Имейл *</label>
                        <input type="email" name="email" value="{{ email }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Телефон</label>
                        <input type="text" name="telephone" value="{{ telephone }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Address -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Адрес за плащане</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Име</label>
                        <input type="text" name="payment_firstname" value="{{ payment_firstname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фамилия</label>
                        <input type="text" name="payment_lastname" value="{{ payment_lastname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фирма</label>
                        <input type="text" name="payment_company" value="{{ payment_company }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 1</label>
                        <input type="text" name="payment_address_1" value="{{ payment_address_1 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 2</label>
                        <input type="text" name="payment_address_2" value="{{ payment_address_2 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Град</label>
                        <input type="text" name="payment_city" value="{{ payment_city }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Пощенски код</label>
                        <input type="text" name="payment_postcode" value="{{ payment_postcode }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping Address -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Адрес за доставка</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Име</label>
                        <input type="text" name="shipping_firstname" value="{{ shipping_firstname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фамилия</label>
                        <input type="text" name="shipping_lastname" value="{{ shipping_lastname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фирма</label>
                        <input type="text" name="shipping_company" value="{{ shipping_company }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 1</label>
                        <input type="text" name="shipping_address_1" value="{{ shipping_address_1 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 2</label>
                        <input type="text" name="shipping_address_2" value="{{ shipping_address_2 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Град</label>
                        <input type="text" name="shipping_city" value="{{ shipping_city }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Пощенски код</label>
                        <input type="text" name="shipping_postcode" value="{{ shipping_postcode }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Products -->
        {% if order_products %}
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Продукти в поръчката</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Продукт
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Количество
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ед. цена
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Сума
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for product in order_products %}
                        <tr>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                    {% if product.model %}
                                    <div class="text-sm text-gray-500">Модел: {{ product.model }}</div>
                                    {% endif %}
                                    {% if product.options %}
                                        {% for option in product.options %}
                                        <div class="text-xs text-gray-500">{{ option.name }}: {{ option.value }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <input type="number" name="products[{{ product.order_product_id }}][quantity]" value="{{ product.quantity }}" min="1" class="w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm">
                            </td>
                            <td class="px-6 py-4 text-right">
                                <input type="number" name="products[{{ product.order_product_id }}][price]" value="{{ product.price|number_format(2, ".", " ") }}" step="0.01" class="w-24 px-2 py-1 border border-gray-300 rounded text-right text-sm">
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium text-gray-900">
                                {{ product.total|number_format(2, ".", " ") }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Order Settings -->
        <div class="bg-white rounded shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Настройки на поръчката</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {% if order_statuses %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Статус</label>
                        <select name="order_status_id" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            {% for status in order_statuses %}
                            <option value="{{ status.order_status_id }}" {% if status.order_status_id == order_status_id %}selected{% endif %}>{{ status.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    {% if payment_methods %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Метод на плащане</label>
                        <select name="payment_code" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            {% for method in payment_methods %}
                            <option value="{{ method.code }}" {% if method.code == payment_code %}selected{% endif %}>{{ method.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    {% if shipping_methods %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Метод на доставка</label>
                        <select name="shipping_code" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            {% for method in shipping_methods %}
                            <option value="{{ method.code }}" {% if method.code == shipping_code %}selected{% endif %}>{{ method.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Коментар</label>
                        <textarea name="comment" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">{{ comment }}</textarea>
                    </div>
                </div>
                
                <div class="mt-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="notify" value="1" class="mr-2">
                        <span class="text-sm text-gray-700">Изпрати известие до клиента при промяна на статуса</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 flex justify-end space-x-4">
                {% if back_url %}
                <a href="{{ back_url }}" class="px-6 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors">
                    Отказ
                </a>
                {% endif %}
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                    Запази промените
                </button>
            </div>
        </div>

    </form>
</main>
