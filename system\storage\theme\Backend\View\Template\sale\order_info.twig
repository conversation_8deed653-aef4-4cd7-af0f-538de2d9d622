<!-- Order Info Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Детайли на поръчка #{{ order_id }}</h1>
            <p class="text-gray-500 mt-1">Преглед на информацията за поръчката</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2">
            {% if back_url %}
            <a href="{{ back_url }}" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-arrow-left-line"></i>
                </div>
                <span>Назад</span>
            </a>
            {% endif %}
            {% if edit_url %}
            <a href="{{ edit_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-edit-line"></i>
                </div>
                <span>Редактиране</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <div class="max-w-7xl space-y-6">
        
        <!-- Order Information -->
        <div class="bg-white rounded shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Информация за поръчката</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-3">Основна информация</h4>
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-medium">Номер:</span> #{{ order_id }}</p>
                            <p class="text-sm"><span class="font-medium">Дата:</span> {{ date_added }}</p>
                            {% if date_modified %}
                            <p class="text-sm"><span class="font-medium">Последна промяна:</span> {{ date_modified }}</p>
                            {% endif %}
                            <p class="text-sm"><span class="font-medium">Статус:</span>
                                <span class="status-badge {{ status_css_class }}">{{ order_status }}</span>
                            </p>
                            <p class="text-sm"><span class="font-medium">Обща сума:</span> <span class="text-lg font-semibold text-primary">{{ total }}</span></p>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-3">Информация за клиента</h4>
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-medium">Име:</span> {{ customer }}</p>
                            {% if email %}
                            <p class="text-sm"><span class="font-medium">Имейл:</span> {{ email }}</p>
                            {% endif %}
                            {% if telephone %}
                            <p class="text-sm"><span class="font-medium">Телефон:</span> {{ telephone }}</p>
                            {% endif %}
                            {% if customer_id %}
                            <p class="text-sm"><span class="font-medium">ID клиент:</span> {{ customer_id }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-3">Плащане и доставка</h4>
                        <div class="space-y-2">
                            {% if payment_method %}
                            <p class="text-sm"><span class="font-medium">Плащане:</span> {{ payment_method }}</p>
                            {% endif %}
                            {% if shipping_method %}
                            <p class="text-sm"><span class="font-medium">Доставка:</span> {{ shipping_method }}</p>
                            {% endif %}
                            {% if currency_code %}
                            <p class="text-sm"><span class="font-medium">Валута:</span> {{ currency_code }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Addresses -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Payment Address -->
            <div class="bg-white rounded shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Адрес за плащане</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-2">
                        {% if payment_firstname or payment_lastname %}
                        <p class="text-sm font-medium">{{ payment_firstname }} {{ payment_lastname }}</p>
                        {% endif %}
                        {% if payment_company %}
                        <p class="text-sm">{{ payment_company }}</p>
                        {% endif %}
                        {% if payment_address_1 %}
                        <p class="text-sm">{{ payment_address_1 }}</p>
                        {% endif %}
                        {% if payment_address_2 %}
                        <p class="text-sm">{{ payment_address_2 }}</p>
                        {% endif %}
                        {% if payment_city or payment_postcode %}
                        <p class="text-sm">{{ payment_city }}{% if payment_postcode %}, {{ payment_postcode }}{% endif %}</p>
                        {% endif %}
                        {% if payment_zone %}
                        <p class="text-sm">{{ payment_zone }}</p>
                        {% endif %}
                        {% if payment_country %}
                        <p class="text-sm">{{ payment_country }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            <div class="bg-white rounded shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Адрес за доставка</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-2">
                        {% if shipping_firstname or shipping_lastname %}
                        <p class="text-sm font-medium">{{ shipping_firstname }} {{ shipping_lastname }}</p>
                        {% endif %}
                        {% if shipping_company %}
                        <p class="text-sm">{{ shipping_company }}</p>
                        {% endif %}
                        {% if shipping_address_1 %}
                        <p class="text-sm">{{ shipping_address_1 }}</p>
                        {% endif %}
                        {% if shipping_address_2 %}
                        <p class="text-sm">{{ shipping_address_2 }}</p>
                        {% endif %}
                        {% if shipping_city or shipping_postcode %}
                        <p class="text-sm">{{ shipping_city }}{% if shipping_postcode %}, {{ shipping_postcode }}{% endif %}</p>
                        {% endif %}
                        {% if shipping_zone %}
                        <p class="text-sm">{{ shipping_zone }}</p>
                        {% endif %}
                        {% if shipping_country %}
                        <p class="text-sm">{{ shipping_country }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Products -->
        {% if order_products %}
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Продукти в поръчката</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Продукт
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Количество
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ед. цена
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Сума
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for product in order_products %}
                        <tr>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    {% if product.image %}
                                    <div class="flex-shrink-0 h-16 w-16 bg-gray-100 rounded">
                                        <img class="h-16 w-16 rounded object-cover object-top" src="{{ product.image }}" alt="{{ product.name }}">
                                    </div>
                                    {% endif %}
                                    <div class="{% if product.image %}ml-4{% endif %}">
                                        <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                        {% if product.model %}
                                        <div class="text-sm text-gray-500">Модел:{{' '}}{{ product.model }}</div>
                                        {% endif %}
                                        {% if product.options %}
                                            {% for option in product.options %}
                                            <div class="text-xs text-gray-500">{{ option.name }}:{{' '}}{{ option.value }}</div>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-center text-sm text-gray-900">
                                {{ product.quantity }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm text-gray-900">
                                {{ product.price }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium text-gray-900">
                                {{ product.total }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Order Totals -->
        {% if order_totals %}
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Общи суми</h3>
            </div>
            <div class="p-6">
                <div class="space-y-2">
                    {% for total in order_totals %}
                    <div class="flex justify-between">
                        <span class="text-sm {% if total.code == 'total' %}font-semibold{% endif %}">{{ total.title }}:</span>
                        <span class="text-sm {% if total.code == 'total' %}font-semibold text-primary{% endif %}">{{ total.text }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Order History -->
        {% if order_histories %}
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">История на поръчката</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for history in order_histories %}
                    <div class="border-l-4 border-primary pl-4">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ history.status }}</p>
                                {% if history.comment %}
                                <p class="text-sm text-gray-600 mt-1">{{ history.comment }}</p>
                                {% endif %}
                            </div>
                            <span class="text-xs text-gray-500">{{ history.date_added }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Order Detail Modal Button -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Действия с поръчката</h3>
            </div>
            <div class="p-6">
                <div class="flex space-x-4">
                    <button id="show-order-detail" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 flex items-center">
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                            <i class="ri-edit-line"></i>
                        </div>
                        Промяна на статус и изпращане на известие
                    </button>
                </div>
            </div>
        </div>

    </div>
</main>

<!-- Order Detail Modal -->
<div id="order-detail-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Детайли на поръчка #{{ order_id }}</h3>
            <button id="close-order-detail" class="text-gray-400 hover:text-gray-500">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-close-line"></i>
                </div>
            </button>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row justify-between mb-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Информация за поръчката</h4>
                    <p class="text-sm"><span class="font-medium">Дата:</span> {{ date_added }}</p>
                    <p class="text-sm"><span class="font-medium">Статус:</span> <span class="status-badge {{ status_css_class }}">{{ order_status }}</span></p>
                    {% if payment_method %}
                    <p class="text-sm"><span class="font-medium">Плащане:</span> {{ payment_method }}</p>
                    {% endif %}
                    <p class="text-sm"><span class="font-medium">Обща сума:</span> {{ total }}</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Информация за клиента</h4>
                    <p class="text-sm"><span class="font-medium">Име:</span> {{ customer }}</p>
                    {% if email %}
                    <p class="text-sm"><span class="font-medium">Имейл:</span> {{ email }}</p>
                    {% endif %}
                    {% if telephone %}
                    <p class="text-sm"><span class="font-medium">Телефон:</span> {{ telephone }}</p>
                    {% endif %}
                </div>
                <div class="mt-4 md:mt-0">
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Адрес за доставка</h4>
                    {% if shipping_firstname or shipping_lastname %}
                    <p class="text-sm">{{ shipping_firstname }} {{ shipping_lastname }}</p>
                    {% endif %}
                    {% if shipping_address_1 %}
                    <p class="text-sm">{{ shipping_address_1 }}</p>
                    {% endif %}
                    {% if shipping_city or shipping_postcode %}
                    <p class="text-sm">{{ shipping_city }}{% if shipping_postcode %}, {{ shipping_postcode }}{% endif %}</p>
                    {% endif %}
                    {% if shipping_country %}
                    <p class="text-sm">{{ shipping_country }}</p>
                    {% endif %}
                </div>
            </div>

            {% if order_products %}
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="font-medium text-gray-800 mb-4">Продукти в поръчката</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Продукт
                                </th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Количество
                                </th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ед. цена
                                </th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Сума
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for product in order_products %}
                            <tr>
                                <td class="px-4 py-4">
                                    <div class="flex items-center">
                                        {% if product.image %}
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 rounded object-cover object-top" src="{{ product.image }}" alt="{{ product.name }}">
                                        </div>
                                        {% endif %}
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                            {% if product.model %}
                                            <div class="text-sm text-gray-500">Модел: {{ product.model }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 text-center text-sm text-gray-900">
                                    {{ product.quantity }}
                                </td>
                                <td class="px-4 py-4 text-right text-sm text-gray-900">
                                    {{ product.price }}
                                </td>
                                <td class="px-4 py-4 text-right text-sm font-medium text-gray-900">
                                    {{ product.total }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        {% if order_totals %}
                        <tfoot>
                            {% for total in order_totals %}
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">{{ total.title }}:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium {% if total.code == 'total' %}text-primary{% else %}text-gray-900{% endif %}">{{ total.text }}</td>
                            </tr>
                            {% endfor %}
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>
            {% endif %}

            {% if order_statuses %}
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="font-medium text-gray-800 mb-4">Промяна на статус</h4>
                <form id="status-update-form" method="post" action="{{ update_status_url }}">
                    <input type="hidden" name="order_id" value="{{ order_id }}">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Нов статус</label>
                            <select name="order_status_id" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                                {% for status in order_statuses %}
                                <option value="{{ status.order_status_id }}" {% if status.order_status_id == order_status_id %}selected{% endif %}>{{ status.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Коментар</label>
                            <input type="text" name="comment" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" placeholder="Опционален коментар">
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" name="notify" value="1" class="mr-2">
                            <span class="text-sm text-gray-700">Изпрати известие до клиента</span>
                        </label>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90">
                            Актуализирай статус
                        </button>
                    </div>
                </form>

                <!-- Quick Status Buttons -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <h5 class="text-sm font-medium text-gray-700 mb-2">Бърза промяна:</h5>
                    <div class="flex flex-wrap items-center gap-2">
                        {% for status in order_statuses %}
                        <button class="status-change-btn px-3 py-1 {% if status.order_status_id == order_status_id %}bg-blue-100 text-blue-700{% else %}bg-white border border-gray-300 text-gray-700{% endif %} rounded-button hover:bg-blue-200 text-xs whitespace-nowrap" data-status-id="{{ status.order_status_id }}" data-order-id="{{ order_id }}">{{ status.name }}</button>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="border-t border-gray-200 pt-6" data-notification-form>
                <h4 class="font-medium text-gray-800 mb-4">Изпращане на известие</h4>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Тема</label>
                    <input type="text" name="subject" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" value="Информация за поръчка #{{ order_id }}">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Съобщение</label>
                    <textarea name="message" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm h-32" placeholder="Въведете текст на съобщението...">Здравейте, {{ customer }}!

Благодарим ви за вашата поръчка #{{ order_id }}.

С уважение,
Екипът на Rakla</textarea>
                </div>
                <input type="hidden" name="order_id" value="{{ order_id }}">
                <div class="flex justify-end">
                    <button data-send-notification class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Изпрати известие</button>
                </div>
            </div>
        </div>
    </div>
</div>
