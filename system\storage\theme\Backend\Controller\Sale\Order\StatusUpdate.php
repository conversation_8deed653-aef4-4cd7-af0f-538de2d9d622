<?php

namespace Theme25\Backend\Controller\Sale\Order;

use Theme25\Helper\MailHelper;

class StatusUpdate extends \Theme25\ControllerSubMethods {

    /**
     * Обработва AJAX заявките за промяна на статус
     */
    public function updateStatus() {
        $json = [];

        if ($this->isPostRequest() && $this->hasPermission('modify', 'sale/order')) {

            F()->log->developer($this->requestPost(), __FILE__, __LINE__);


            $order_id = (int)$this->requestPost('order_id');
            $order_status_id = (int)$this->requestPost('order_status_id');
            $comment = $this->requestPost('comment', '');
            $notify = (bool)$this->requestPost('notify', false);

            F()->log->developer($order_id, __FILE__, __LINE__);
            F()->log->developer($order_status_id, __FILE__, __LINE__);
            F()->log->developer($comment, __FILE__, __LINE__);
            F()->log->developer($notify, __FILE__, __LINE__);

            if ($this->validateStatusUpdateData($order_id, $order_status_id)) {
                try {


                    F()->log->developer('>>> processStatusUpdate', __FILE__, __LINE__);
                        

                    $this->processStatusUpdate($order_id, $order_status_id, $comment, $notify);

                    F()->log->developer('<<< processStatusUpdate', __FILE__, __LINE__);

                    $json['success'] = 'Статусът на поръчката е актуализиран успешно';
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при актуализиране на статуса: ' . $e->getMessage();
                }
            } else {
                $json['error'] = 'Невалидни данни за актуализиране на статуса';
            }
        } else {
            $json['error'] = 'Няmate права за тази операция';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира данните за актуализиране на статус
     *
     * @param int $order_id ID на поръчката
     * @param int $order_status_id ID на новия статус
     * @return bool Резултат от валидацията
     */
    private function validateStatusUpdateData($order_id, $order_status_id) {
        // Проверка за валидни ID-та
        if (!$order_id || !$order_status_id) {
            return false;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/order' => 'orders',
            'localisation/order_status' => 'orderStatuses'
        ]);

        // Проверка дали поръчката съществува
        $order_info = $this->orders->getOrder($order_id);

        if (!$order_info) {
            return false;
        }

        // Проверка дали статусът съществува
        $status_info = $this->orderStatuses->getOrderStatus($order_status_id);

        if (!$status_info) {
            return false;
        }

        // Проверка дали новият статус е различен от текущия
        // if ($order_info['order_status_id'] == $order_status_id) {
        //     return false;
        // }

        return true;
    }

    /**
     * Обработва актуализирането на статуса
     *
     * @param int $order_id ID на поръчката
     * @param int $order_status_id ID на новия статус
     * @param string $comment Коментар
     * @param bool $notify Дали да се изпрати известие
     */
    private function processStatusUpdate($order_id, $order_status_id, $comment, $notify) {
        // Зареждане на модела за поръчки
        $this->loadModelAs('sale/order', 'orders');

        F()->log->developer('>>> addOrderHistory', __FILE__, __LINE__);
        // Добавяне на запис в историята на поръчката
        $this->orders->addOrderHistory($order_id, $order_status_id, $comment, $notify);

        F()->log->developer('<<< addOrderHistory', __FILE__, __LINE__);
        F()->log->developer('is notify: ' . json_encode($notify), __FILE__, __LINE__);


        // Ако е необходимо известие, изпращаме имейл
        if ($notify) {

            F()->log->developer('>>> sendStatusUpdateNotification', __FILE__, __LINE__);
            $this->sendStatusUpdateNotification($order_id, $order_status_id, $comment);
            F()->log->developer('<<< sendStatusUpdateNotification', __FILE__, __LINE__);
        }

        
        // Логиране на промяната
        $this->logStatusChange($order_id, $order_status_id, $comment);
    }

    /**
     * Изпраща известие за промяна на статус
     *
     * @param int $order_id ID на поръчката
     * @param int $order_status_id ID на новия статус
     * @param string $comment Коментар
     */
    private function sendStatusUpdateNotification($order_id, $order_status_id, $comment) {
        try {
            // Получаване на информацията за поръчката
            $order_info = $this->orders->getOrder($order_id);
            
            if (!$order_info || !$order_info['email']) {
                return;
            }

            // Получаване на информацията за статуса
            $this->loadModelAs('localisation/order_status', 'orderStatuses');
            $status_info = $this->orderStatuses->getOrderStatus($order_status_id);
            
            if (!$status_info) {
                return;
            }

            // Подготовка на съдържанието на имейла
            $subject = sprintf('Актуализация на поръчка #%s', $order_info['order_id']);
            $message = $this->prepareStatusUpdateEmailContent($order_info, $status_info, $comment);

            // Изпращане на имейла
            $this->sendEmail($order_info['email'], $subject, $message);

        } catch (Exception $e) {
            // Логиране на грешката, но не спираме процеса
            error_log('Грешка при изпращане на известие за статус: ' . $e->getMessage());
        }
    }

    /**
     * Подготвя съдържанието на имейла за промяна на статус
     *
     * @param array $order_info Информация за поръчката
     * @param array $status_info Информация за статуса
     * @param string $comment Коментар
     * @return string Съдържание на имейла
     */
    private function prepareStatusUpdateEmailContent($order_info, $status_info, $comment) {
        $message = "Здравейте,\n\n";
        $message .= sprintf("Статусът на вашата поръчка #%s е актуализиран.\n\n", $order_info['order_id']);
        $message .= sprintf("Нов статус: %s\n", $status_info['name']);
        
        if (!empty($comment)) {
            $message .= sprintf("Коментар: %s\n", $comment);
        }
        
        $message .= "\nБлагодарим ви за доверието!\n";
        $message .= sprintf("Екипът на %s", $this->getConfig('config_name'));

        return $message;
    }

    /**
     * Изпраща имейл
     *
     * @param string $to Получател
     * @param string $subject Тема
     * @param string $message Съдържание
     */
    private function sendEmail($to, $subject, $message) {

        $mailMessage = [
            'to' => $to,
            'from' => MailHelper::getMailOption('from_email'),
            'sender' => MailHelper::getMailOption('sender'),
            'subject' => $subject,
            'html' => $message
        ];

        F()->log->developer($mailMessage, __FILE__, __LINE__);

        MailHelper::send([], $mailMessage, false, null, 1);

        F()->log->developer('<<< sendEmail', __FILE__, __LINE__);
    }

    /**
     * Логира промяната на статус
     *
     * @param int $order_id ID на поръчката
     * @param int $order_status_id ID на новия статус
     * @param string $comment Коментар
     */
    private function logStatusChange($order_id, $order_status_id, $comment) {
        // Тук може да се добави логика за логиране в специален файл или база данни
        $log_message = sprintf(
            'Order #%d status changed to %d by user %s. Comment: %s',
            $order_id,
            $order_status_id,
            $this->getUser('username') ?? 'unknown',
            $comment
        );
        
        error_log($log_message);
        F()->log->developer($log_message, __FILE__, __LINE__);
    }

    /**
     * Групова промяна на статус на множество поръчки
     */
    public function bulkUpdateStatus() {
        $json = [];

        if ($this->isPostRequest() && $this->hasPermission('modify', 'sale/order')) {
            $selected = $this->requestPost('selected', []);
            $order_status_id = (int)$this->requestPost('order_status_id');
            $comment = $this->requestPost('comment', '');
            $notify = (bool)$this->requestPost('notify', false);

            if (empty($selected) || !$order_status_id) {
                $json['error'] = 'Невалидни данни за групова промяна на статус';
            } else {
                $success_count = 0;
                $error_count = 0;

                foreach ($selected as $order_id) {
                    try {
                        if ($this->validateStatusUpdateData((int)$order_id, $order_status_id)) {
                            $this->processStatusUpdate((int)$order_id, $order_status_id, $comment, $notify);
                            $success_count++;
                        } else {
                            $error_count++;
                        }
                    } catch (Exception $e) {
                        $error_count++;
                    }
                }

                if ($success_count > 0) {
                    $json['success'] = sprintf('Успешно актуализирани %d поръчки', $success_count);
                    if ($error_count > 0) {
                        $json['warning'] = sprintf('Грешки при %d поръчки', $error_count);
                    }
                } else {
                    $json['error'] = 'Няма успешно актуализирани поръчки';
                }
            }
        } else {
            $json['error'] = 'Няmate права за тази операция';
        }

        $this->setJSONResponseOutput($json);
    }
}
