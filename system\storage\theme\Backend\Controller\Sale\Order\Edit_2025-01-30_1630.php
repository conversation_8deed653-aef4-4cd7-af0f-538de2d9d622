<?php

namespace Theme25\Backend\Controller\Sale\Order;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'order-edit.js',
        ]);
    }

    public function prepareOrderForm() {
        $order_id = (int)$this->requestGet('order_id');
        
        if (!$order_id) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return;
        }

        // Обработка на POST заявка
        if ($this->isPostRequest()) {
            $this->processOrderUpdate($order_id);
            return;
        }

        $this->prepareOrderData($order_id)
             ->prepareOrderProducts($order_id)
             ->prepareOrderTotals($order_id)
             ->prepareCustomerData($order_id)
             ->prepareOrderStatuses()
             ->preparePaymentMethods()
             ->prepareShippingMethods();

        $this->setData([
            'back_url' => $this->getAdminLink('sale/order'),
            'save_url' => $this->getAdminLink('sale/order/edit', 'order_id=' . $order_id),
            'order_id' => $order_id
        ]);
    }

    /**
     * Обработва актуализацията на поръчката
     *
     * @param int $order_id ID на поръчката
     */
    private function processOrderUpdate($order_id) {
        if (!$this->hasPermission('modify', 'sale/order')) {
            $this->setSession('error', 'Няmate права за редактиране на поръчки');
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return;
        }

        $this->loadModelAs('sale/order', 'orders');
        
        try {
            // Получаване на данните от формата
            $data = $this->requestPost();

            F()->log->developer($data, __FILE__, __LINE__);
            
            // Валидация на данните
            if ($this->validateOrderData($data)) {
                // Актуализиране на поръчката
               // $this->orders->editOrder($order_id, $data);
                
                // Добавяне на история, ако е променен статуса
                if (isset($data['order_status_id']) && !empty($data['order_status_id'])) {
                    $comment = $data['comment'] ?? '';
                    $notify = isset($data['notify']) ? (bool)$data['notify'] : false;
                    
                    $this->orders->addOrderHistory($order_id, $data['order_status_id'], $comment, $notify);
                }

                F()->log->developer('>>> editOrder', __FILE__, __LINE__);
                
                $this->setSession('success', 'Поръчката е актуализирана успешно');
                $this->redirectResponse($this->getAdminLink('sale/order/info', 'order_id=' . $order_id));
            }
        } catch (Exception $e) {
            $this->setSession('error', 'Грешка при актуализиране на поръчката: ' . $e->getMessage());
        }
    }

    /**
     * Валидира данните за поръчката
     *
     * @param array $data Данни за валидация
     * @return bool Резултат от валидацията
     */
    private function validateOrderData($data) {
        $errors = [];

        // Валидация на задължителни полета
        if (empty($data['firstname'])) {
            $errors[] = 'Името е задължително';
        }

        if (empty($data['lastname'])) {
            $errors[] = 'Фамилията е задължителна';
        }

        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Валиден имейл е задължителен';
        }

        if (!empty($errors)) {
            $this->setSession('error', implode('<br>', $errors));
            return false;
        }

        return true;
    }

    /**
     * Подготвя основните данни за поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderData($order_id) {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/order' => 'orders'
        ]);

        // Получаване на информацията за поръчката
        $order_info = $this->orders->getOrder($order_id);

        if (!$order_info) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return $this;
        }

        // Подготовка на данните за поръчката
        $order_data = [
            'order_id' => $order_info['order_id'],
            'customer_id' => $order_info['customer_id'],
            'customer_group_id' => $order_info['customer_group_id'],
            'firstname' => $order_info['firstname'],
            'lastname' => $order_info['lastname'],
            'email' => $order_info['email'],
            'telephone' => $order_info['telephone'],
            'fax' => $order_info['fax'],
            'order_status_id' => $order_info['order_status_id'],
            'comment' => $order_info['comment'],
            'total' => $order_info['total'],
            'currency_code' => $order_info['currency_code'],
            'currency_value' => $order_info['currency_value'],
            'date_added' => $order_info['date_added'],
            'date_modified' => $order_info['date_modified'],
            
            // Адрес за плащане
            'payment_firstname' => $order_info['payment_firstname'],
            'payment_lastname' => $order_info['payment_lastname'],
            'payment_company' => $order_info['payment_company'],
            'payment_address_1' => $order_info['payment_address_1'],
            'payment_address_2' => $order_info['payment_address_2'],
            'payment_city' => $order_info['payment_city'],
            'payment_postcode' => $order_info['payment_postcode'],
            'payment_country_id' => $order_info['payment_country_id'],
            'payment_zone_id' => $order_info['payment_zone_id'],
            'payment_method' => $order_info['payment_method'],
            'payment_code' => $order_info['payment_code'],
            
            // Адрес за доставка
            'shipping_firstname' => $order_info['shipping_firstname'],
            'shipping_lastname' => $order_info['shipping_lastname'],
            'shipping_company' => $order_info['shipping_company'],
            'shipping_address_1' => $order_info['shipping_address_1'],
            'shipping_address_2' => $order_info['shipping_address_2'],
            'shipping_city' => $order_info['shipping_city'],
            'shipping_postcode' => $order_info['shipping_postcode'],
            'shipping_country_id' => $order_info['shipping_country_id'],
            'shipping_zone_id' => $order_info['shipping_zone_id'],
            'shipping_method' => $order_info['shipping_method'],
            'shipping_code' => $order_info['shipping_code']
        ];

        $this->setData($order_data);

        return $this;
    }

    /**
     * Подготвя продуктите в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderProducts($order_id) {
        // Получаване на продуктите в поръчката
        $order_products = $this->orders->getOrderProducts($order_id);
        
        $products = [];
        
        foreach ($order_products as $product) {
            // Получаване на опциите за продукта
            $options = $this->orders->getOrderOptions($order_id, $product['order_product_id']);
            
            $product_options = [];
            foreach ($options as $option) {
                $product_options[] = [
                    'name' => $option['name'],
                    'value' => $option['value']
                ];
            }
            
            $products[] = [
                'order_product_id' => $product['order_product_id'],
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'quantity' => $product['quantity'],
                'price' => $product['price'],
                'total' => $product['total'],
                'tax' => $product['tax'],
                'reward' => $product['reward'],
                'options' => $product_options
            ];
        }

        $this->setData('order_products', $products);

        return $this;
    }

    /**
     * Подготвя общите суми на поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderTotals($order_id) {
        // Получаване на общите суми
        $order_totals = $this->orders->getOrderTotals($order_id);
        
        $totals = [];
        
        foreach ($order_totals as $total) {
            $totals[] = [
                'title' => $total['title'],
                'value' => $total['value'],
                'code' => $total['code'],
                'sort_order' => $total['sort_order']
            ];
        }

        $this->setData('order_totals', $totals);

        return $this;
    }

    /**
     * Подготвя данните за клиента
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareCustomerData($order_id) {
        $customer_id = $this->getData('customer_id');
        
        if ($customer_id) {
            $this->loadModelAs('customer/customer', 'customers');
            $customer_info = $this->customers->getCustomer($customer_id);
            
            if ($customer_info) {
                $this->setData('customer_info', $customer_info);
            }
        }

        return $this;
    }

    /**
     * Подготвя статусите на поръчки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderStatuses() {
        // Зареждане на модела за статуси
        $this->loadModelAs('localisation/order_status', 'orderStatuses');
        
        // Получаване на всички статуси
        $order_statuses = $this->orderStatuses->getOrderStatuses();

        $this->setData('order_statuses', $order_statuses);

        return $this;
    }

    /**
     * Подготвя методите за плащане
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePaymentMethods() {
        // Тук може да се добави логика за зареждане на методи за плащане
        $payment_methods = [
            ['code' => 'bank_transfer', 'name' => 'Банков превод'],
            ['code' => 'cod', 'name' => 'Наложен платеж'],
            ['code' => 'credit_card', 'name' => 'Кредитна карта']
        ];

        $this->setData('payment_methods', $payment_methods);

        return $this;
    }

    /**
     * Подготвя методите за доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareShippingMethods() {
        // Тук може да се добави логика за зареждане на методи за доставка
        $shipping_methods = [
            ['code' => 'flat', 'name' => 'Фиксирана доставка'],
            ['code' => 'pickup', 'name' => 'Вземане от офис'],
            ['code' => 'free', 'name' => 'Безплатна доставка']
        ];

        $this->setData('shipping_methods', $shipping_methods);

        return $this;
    }

}
