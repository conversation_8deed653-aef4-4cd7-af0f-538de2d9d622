/**
 * JavaScript модул за редактиране на поръчка
 * Разширява основния BackendModule клас
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за редактиране на поръчка
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initOrderEditModule();
        } else {
            console.error('ORDER-EDIT.JS: BackendModule is not defined!');
        }
    });

    // Добавяне на функционалност за редактиране на поръчка към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || '';
        } catch (e) {
            console.error('Error parsing URL params for user_token:', e);
            BackendModule.config.userToken = '';
        }

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за редактиране на поръчка
             */
            initOrderEditModule: function() {
                this.initOrderEditForm();
                this.initOrderValidation();
                this.initAddressHandling();
                this.initProductManagement();
                this.initOrderTotalsCalculation();
            },

            /**
             * Инициализация на формата за редактиране на поръчка
             */
            initOrderEditForm: function() {
                const orderForm = document.getElementById('order-edit-form');
                
                if (orderForm) {
                    orderForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // Валидация на формата
                        if (!BackendModule.validateOrderForm(this)) {
                            return;
                        }
                        
                        const formData = new FormData(this);
                        
                        // Показване на loading индикатор
                        BackendModule.showLoadingIndicator();
                        
                        const actionUrl = this.action + '&user_token=' + BackendModule.config.userToken;
                        
                        fetch(actionUrl, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            BackendModule.hideLoadingIndicator();
                            
                            if (data.success) {
                                BackendModule.showOrderAlert('success', data.success);
                                // Пренасочване към страницата с информация за поръчката
                                setTimeout(() => {
                                    const orderId = document.querySelector('[name="order_id"]').value;
                                    const infoUrl = window.location.origin + window.location.pathname + 
                                                  '?route=sale/order/info&order_id=' + orderId + 
                                                  '&user_token=' + BackendModule.config.userToken;
                                    window.location.href = infoUrl;
                                }, 1500);
                            } else if (data.error) {
                                BackendModule.showOrderAlert('error', data.error);
                            }
                        })
                        .catch(error => {
                            BackendModule.hideLoadingIndicator();
                            console.error('Error:', error);
                            BackendModule.showOrderAlert('error', 'Възникна грешка при запазване на поръчката');
                        });
                    });
                }
            },

            /**
             * Инициализация на валидацията на формата
             */
            initOrderValidation: function() {
                // Валидация в реalno време
                const requiredFields = document.querySelectorAll('[required]');
                
                requiredFields.forEach(field => {
                    field.addEventListener('blur', function() {
                        BackendModule.validateField(this);
                    });
                    
                    field.addEventListener('input', function() {
                        // Премахване на грешката при въвеждане
                        this.classList.remove('border-red-500');
                        const errorMsg = this.parentNode.querySelector('.error-message');
                        if (errorMsg) {
                            errorMsg.remove();
                        }
                    });
                });
                
                // Валидация на имейл полета
                const emailFields = document.querySelectorAll('input[type="email"]');
                emailFields.forEach(field => {
                    field.addEventListener('blur', function() {
                        BackendModule.validateEmailField(this);
                    });
                });
            },

            /**
             * Валидация на цялата форма
             */
            validateOrderForm: function(form) {
                let isValid = true;
                const requiredFields = form.querySelectorAll('[required]');
                
                // Изчистване на предишни грешки
                form.querySelectorAll('.error-message').forEach(msg => msg.remove());
                form.querySelectorAll('.border-red-500').forEach(field => {
                    field.classList.remove('border-red-500');
                });
                
                requiredFields.forEach(field => {
                    if (!BackendModule.validateField(field)) {
                        isValid = false;
                    }
                });
                
                // Валидация на имейл полета
                const emailFields = form.querySelectorAll('input[type="email"]');
                emailFields.forEach(field => {
                    if (!BackendModule.validateEmailField(field)) {
                        isValid = false;
                    }
                });
                
                return isValid;
            },

            /**
             * Валидация на отделно поле
             */
            validateField: function(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';
                
                if (field.hasAttribute('required') && !value) {
                    isValid = false;
                    errorMessage = 'Това поле е задължително';
                }
                
                if (!isValid) {
                    field.classList.add('border-red-500');
                    BackendModule.showFieldError(field, errorMessage);
                } else {
                    field.classList.remove('border-red-500');
                    BackendModule.hideFieldError(field);
                }
                
                return isValid;
            },

            /**
             * Валидация на имейл поле
             */
            validateEmailField: function(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';
                
                if (value && !BackendModule.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Моля, въведете валиден имейл адрес';
                }
                
                if (!isValid) {
                    field.classList.add('border-red-500');
                    BackendModule.showFieldError(field, errorMessage);
                } else {
                    field.classList.remove('border-red-500');
                    BackendModule.hideFieldError(field);
                }
                
                return isValid;
            },

            /**
             * Проверка за валиден имейл
             */
            isValidEmail: function(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            },

            /**
             * Показване на грешка за поле
             */
            showFieldError: function(field, message) {
                // Премахване на съществуваща грешка
                this.hideFieldError(field);
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                errorDiv.textContent = message;
                
                field.parentNode.appendChild(errorDiv);
            },

            /**
             * Скриване на грешка за поле
             */
            hideFieldError: function(field) {
                const errorMsg = field.parentNode.querySelector('.error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }
            },

            /**
             * Инициализация на работата с адреси
             */
            initAddressHandling: function() {
                // Копиране на адрес за плащане към адрес за доставка
                const copyAddressBtn = document.getElementById('copy-billing-address');
                
                if (copyAddressBtn) {
                    copyAddressBtn.addEventListener('click', function() {
                        BackendModule.copyBillingToShipping();
                    });
                }
                
                // Автоматично попълване на адреси при избор на клиент
                const customerSelect = document.getElementById('customer-select');
                if (customerSelect) {
                    customerSelect.addEventListener('change', function() {
                        const customerId = this.value;
                        if (customerId) {
                            BackendModule.loadCustomerAddresses(customerId);
                        }
                    });
                }
            },

            /**
             * Копиране на адрес за плащане към адрес за доставка
             */
            copyBillingToShipping: function() {
                const billingFields = [
                    'payment_firstname', 'payment_lastname', 'payment_company',
                    'payment_address_1', 'payment_address_2', 'payment_city',
                    'payment_postcode', 'payment_country_id', 'payment_zone_id'
                ];
                
                const shippingFields = [
                    'shipping_firstname', 'shipping_lastname', 'shipping_company',
                    'shipping_address_1', 'shipping_address_2', 'shipping_city',
                    'shipping_postcode', 'shipping_country_id', 'shipping_zone_id'
                ];
                
                billingFields.forEach((billingField, index) => {
                    const billingInput = document.querySelector(`[name="${billingField}"]`);
                    const shippingInput = document.querySelector(`[name="${shippingFields[index]}"]`);
                    
                    if (billingInput && shippingInput) {
                        shippingInput.value = billingInput.value;
                    }
                });
                
                BackendModule.showOrderAlert('success', 'Адресът за плащане е копиран към адреса за доставка');
            },

            /**
             * Зареждане на адресите на клиент
             */
            loadCustomerAddresses: function(customerId) {
                const formData = new FormData();
                formData.append('customer_id', customerId);
                
                const addressUrl = window.location.origin + window.location.pathname + '?route=sale/order/getCustomerAddresses&user_token=' + BackendModule.config.userToken;
                
                fetch(addressUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.addresses) {
                        BackendModule.populateAddressFields(data.addresses);
                    }
                })
                .catch(error => {
                    console.error('Error loading customer addresses:', error);
                });
            },

            /**
             * Попълване на полетата за адреси
             */
            populateAddressFields: function(addresses) {
                if (addresses.billing) {
                    Object.keys(addresses.billing).forEach(key => {
                        const field = document.querySelector(`[name="payment_${key}"]`);
                        if (field) {
                            field.value = addresses.billing[key];
                        }
                    });
                }
                
                if (addresses.shipping) {
                    Object.keys(addresses.shipping).forEach(key => {
                        const field = document.querySelector(`[name="shipping_${key}"]`);
                        if (field) {
                            field.value = addresses.shipping[key];
                        }
                    });
                }
            },

            /**
             * Показване на loading индикатор
             */
            showLoadingIndicator: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }
            },

            /**
             * Скриване на loading индикатор
             */
            hideLoadingIndicator: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="ri-save-line mr-2"></i>Запази промените';
                }
            },

            /**
             * Инициализация на управлението на продукти
             */
            initProductManagement: function() {
                // Добавяне на нов продукт
                const addProductBtn = document.getElementById('add-product-btn');
                if (addProductBtn) {
                    addProductBtn.addEventListener('click', function() {
                        BackendModule.showAddProductModal();
                    });
                }

                // Премахване на продукт
                const removeProductBtns = document.querySelectorAll('.remove-product-btn');
                removeProductBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const productRow = this.closest('tr');
                        if (productRow && confirm('Сигурни ли сте, че искате да премахнете този продукт?')) {
                            productRow.remove();
                            BackendModule.calculateOrderTotals();
                        }
                    });
                });

                // Промяна на количество
                const quantityInputs = document.querySelectorAll('.product-quantity');
                quantityInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.calculateOrderTotals();
                    });

                    input.addEventListener('input', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.calculateOrderTotals();
                    });
                });

                // Промяна на цена
                const priceInputs = document.querySelectorAll('.product-price');
                priceInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.calculateOrderTotals();
                    });

                    input.addEventListener('input', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.calculateOrderTotals();
                    });
                });

                // Първоначално изчисляване на сумите
                BackendModule.calculateOrderTotals();
            },

            /**
             * Показване на модал за добавяне на продукт
             */
            showAddProductModal: function() {
                // Създаване на модален прозорец
                const modal = document.createElement('div');
                modal.id = 'add-product-modal';
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg w-full max-w-lg mx-4">
                        <div class="flex justify-between items-center p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">Добавяне на продукт</h3>
                            <button class="close-modal text-gray-400 hover:text-gray-500">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            <form id="add-product-form">
                                <div class="mb-4 relative">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Продукт</label>
                                    <input type="text" id="product-search" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" placeholder="Търсене на продукт..." autocomplete="off">
                                    <div id="product-suggestions" class="hidden absolute z-10 w-full bg-white border border-gray-300 rounded-button mt-1 max-h-48 overflow-y-auto shadow-lg"></div>
                                    <input type="hidden" id="selected-product-id" name="product_id" value="">
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Количество</label>
                                    <input type="number" name="quantity" min="1" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Цена</label>
                                    <input type="number" name="price" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                                </div>
                                <div class="flex justify-end space-x-2">
                                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Отказ</button>
                                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90">Добави продукт</button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;

                // Добавяне на event listeners
                modal.addEventListener('click', (e) => {
                    if (e.target === modal || e.target.classList.contains('close-modal')) {
                        document.body.removeChild(modal);
                        document.body.style.overflow = 'auto';
                    }
                });

                // Обработка на формата
                modal.querySelector('#add-product-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    BackendModule.addProductToOrder(new FormData(e.target));
                    document.body.removeChild(modal);
                    document.body.style.overflow = 'auto';
                });

                // Търсене на продукти
                const productSearch = modal.querySelector('#product-search');
                productSearch.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length === 0) {
                        // Показване на първите 10 продукта при празно поле
                        BackendModule.loadInitialProducts();
                    } else if (query.length >= 2) {
                        BackendModule.searchProducts(query, false);
                    } else {
                        document.getElementById('product-suggestions').classList.add('hidden');
                    }
                });

                // Показване на първите продукти при фокус
                productSearch.addEventListener('focus', function() {
                    if (this.value.trim().length === 0) {
                        BackendModule.loadInitialProducts();
                    } else if (this.value.trim().length >= 2) {
                        BackendModule.searchProducts(this.value.trim(), false);
                    }
                });

                // Скриване на предложенията при загуба на фокус (с малко забавяне за кликване)
                productSearch.addEventListener('blur', function() {
                    setTimeout(() => {
                        document.getElementById('product-suggestions').classList.add('hidden');
                    }, 200);
                });

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';
            },

            /**
             * Зареждане на първоначални продукти
             */
            loadInitialProducts: function() {
                // Нулиране на pagination данните
                BackendModule.productSearch = {
                    currentPage: 0,
                    hasMore: true,
                    isLoading: false,
                    query: ''
                };

                const formData = new FormData();
                formData.append('limit', '10');
                formData.append('start', '0');

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts&user_token=' + BackendModule.config.userToken;

                BackendModule.productSearch.isLoading = true;
                BackendModule.showProductLoadingIndicator();

                fetch(searchUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.productSearch.hasMore = data.has_more || false;
                    BackendModule.productSearch.currentPage = 1;
                    BackendModule.displayProductSuggestions(data.products || [], true);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                })
                .catch(error => {
                    console.error('Error loading initial products:', error);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                });
            },

            /**
             * Търсене на продукти
             */
            searchProducts: function(query, append = false) {
                // Ако не е append заявка, нулираме pagination данните
                if (!append) {
                    BackendModule.productSearch = {
                        currentPage: 0,
                        hasMore: true,
                        isLoading: false,
                        query: query
                    };
                }

                if (BackendModule.productSearch.isLoading) {
                    return;
                }

                const start = append ? BackendModule.productSearch.currentPage * 10 : 0;

                const formData = new FormData();
                formData.append('search', query);
                formData.append('limit', '10');
                formData.append('start', start.toString());

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts&user_token=' + BackendModule.config.userToken;

                BackendModule.productSearch.isLoading = true;
                if (append) {
                    BackendModule.showProductLoadingIndicator();
                }

                fetch(searchUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.productSearch.hasMore = data.has_more || false;
                    BackendModule.productSearch.currentPage++;
                    BackendModule.displayProductSuggestions(data.products || [], !append);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                })
                .catch(error => {
                    console.error('Error searching products:', error);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                });
            },

            /**
             * Показване на предложения за продукти
             */
            displayProductSuggestions: function(products, clearFirst = true) {
                const suggestionsDiv = document.getElementById('product-suggestions');

                if (clearFirst) {
                    suggestionsDiv.innerHTML = '';
                }

                if (products.length === 0 && clearFirst) {
                    suggestionsDiv.innerHTML = '<div class="p-2 text-gray-500 text-sm">Няма намерени продукти</div>';
                    suggestionsDiv.classList.remove('hidden');
                    return;
                }

                products.forEach(product => {
                    const suggestion = document.createElement('div');
                    suggestion.className = 'p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0';

                    const imageHtml = product.image ?
                        `<img class="w-10 h-10 rounded object-cover object-top mr-3" src="${product.image}" alt="${product.name}">` :
                        '<div class="w-10 h-10 bg-gray-200 rounded mr-3 flex items-center justify-center"><i class="ri-image-line text-gray-400"></i></div>';

                    suggestion.innerHTML = `
                        <div class="flex items-center">
                            ${imageHtml}
                            <div class="flex-1">
                                <div class="font-medium text-sm">${product.name}</div>
                                <div class="text-xs text-gray-500">Модел: ${product.model || 'N/A'} | Цена: ${product.price} лв.</div>
                            </div>
                        </div>
                    `;

                    suggestion.addEventListener('click', function() {
                        document.getElementById('product-search').value = product.name;
                        document.querySelector('[name="price"]').value = parseFloat(product.price).toFixed(2);
                        document.getElementById('selected-product-id').value = product.product_id;

                        // Запазване на данните за продукта за по-късно използване
                        document.getElementById('selected-product-id').setAttribute('data-product-name', product.name);
                        document.getElementById('selected-product-id').setAttribute('data-product-model', product.model || '');
                        document.getElementById('selected-product-id').setAttribute('data-product-image', product.image_large || product.image || '');

                        suggestionsDiv.classList.add('hidden');
                    });

                    suggestionsDiv.appendChild(suggestion);
                });

                // Добавяне на scroll listener за infinite scroll
                if (clearFirst) {
                    BackendModule.setupInfiniteScroll();
                }

                suggestionsDiv.classList.remove('hidden');
            },

            /**
             * Добавяне на продукт към поръчката
             */
            addProductToOrder: function(formData) {
                const productName = document.getElementById('product-search').value;
                const quantity = formData.get('quantity');
                const price = formData.get('price');
                const productId = document.getElementById('selected-product-id').value || 0;

                // Получаване на запазените данни за продукта
                const selectedProductElement = document.getElementById('selected-product-id');
                const productModel = selectedProductElement.getAttribute('data-product-model') || '';
                const productImage = selectedProductElement.getAttribute('data-product-image') || '';

                if (!productName || !quantity || !price || !productId) {
                    BackendModule.showOrderAlert('error', 'Моля, изберете продукт и попълнете всички полета');
                    return;
                }

                // Добавяне на ред в таблицата с продукти
                const productsTable = document.getElementById('order-products-table');
                const tbody = productsTable.querySelector('tbody');

                const timestamp = Date.now();
                const total = (parseFloat(quantity) * parseFloat(price)).toFixed(2);

                // Подготовка на HTML за изображението
                let imageHtml = '';
                if (productImage) {
                    imageHtml = `<img class="h-16 w-16 rounded object-cover object-top" src="${productImage}" alt="${productName}">`;
                } else {
                    imageHtml = '<i class="ri-image-line text-gray-400"></i>';
                }

                const row = document.createElement('tr');
                row.setAttribute('data-product-row', timestamp);
                row.innerHTML = `
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-16 w-16 bg-gray-100 rounded">
                                ${imageHtml}
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${productName}</div>
                                ${productModel ? `<div class="text-sm text-gray-500">Модел: ${productModel}</div>` : ''}
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 text-center">
                        <input type="number" name="products[${timestamp}][quantity]" value="${quantity}" min="1" class="product-quantity w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm" data-product-id="${timestamp}">
                    </td>
                    <td class="px-6 py-4 text-right">
                        <input type="number" name="products[${timestamp}][price]" value="${parseFloat(price).toFixed(2)}" step="0.01" class="product-price w-24 px-2 py-1 border border-gray-300 rounded text-right text-sm" data-product-id="${timestamp}">
                    </td>
                    <td class="px-6 py-4 text-right text-sm font-medium text-gray-900 product-total" data-product-id="${timestamp}">
                        ${total}
                    </td>
                    <td class="px-6 py-4 text-center">
                        <button type="button" class="remove-product-btn text-red-500 hover:text-red-700 transition-colors" data-product-id="${timestamp}">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-delete-bin-line"></i>
                            </div>
                        </button>
                    </td>
                    <input type="hidden" name="products[${timestamp}][product_id]" value="${productId}">
                    <input type="hidden" name="products[${timestamp}][name]" value="${productName}">
                `;

                tbody.appendChild(row);

                // Добавяне на event listeners за новия ред
                const quantityInput = row.querySelector('.product-quantity');
                const priceInput = row.querySelector('.product-price');
                const removeBtn = row.querySelector('.remove-product-btn');

                quantityInput.addEventListener('change', function() {
                    BackendModule.updateProductTotal(this);
                    BackendModule.calculateOrderTotals();
                });

                quantityInput.addEventListener('input', function() {
                    BackendModule.updateProductTotal(this);
                    BackendModule.calculateOrderTotals();
                });

                priceInput.addEventListener('change', function() {
                    BackendModule.updateProductTotal(this);
                    BackendModule.calculateOrderTotals();
                });

                priceInput.addEventListener('input', function() {
                    BackendModule.updateProductTotal(this);
                    BackendModule.calculateOrderTotals();
                });

                removeBtn.addEventListener('click', function() {
                    if (confirm('Сигурни ли сте, че искате да премахнете този продукт?')) {
                        row.remove();
                        BackendModule.calculateOrderTotals();
                    }
                });

                BackendModule.calculateOrderTotals();
                BackendModule.showOrderAlert('success', 'Продуктът е добавен успешно');
            },

            /**
             * Актуализиране на общата сума за продукт
             */
            updateProductTotal: function(input) {
                const row = input.closest('tr');
                const quantity = parseFloat(row.querySelector('.product-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.product-price').value) || 0;
                const total = quantity * price;

                row.querySelector('.product-total').textContent = total.toFixed(2);
            },

            /**
             * Инициализация на изчисляването на общи суми
             */
            initOrderTotalsCalculation: function() {
                // Автоматично изчисляване при промяна на данни
                const form = document.getElementById('order-edit-form');
                if (form) {
                    form.addEventListener('change', function(e) {
                        if (e.target.classList.contains('product-quantity') ||
                            e.target.classList.contains('product-price')) {
                            BackendModule.calculateOrderTotals();
                        }
                    });
                }
            },

            /**
             * Изчисляване на общите суми на поръчката
             */
            calculateOrderTotals: function() {
                let subtotal = 0;

                // Изчисляване на подобща сума от продуктите
                const productTotals = document.querySelectorAll('.product-total');
                productTotals.forEach(totalCell => {
                    const value = totalCell.textContent.replace(/\s/g, '').replace(',', '.');
                    subtotal += parseFloat(value) || 0;
                });

                // Намиране на контейнера с общите суми
                const totalsContainer = document.getElementById('order-totals-container');
                if (!totalsContainer) {
                    return;
                }

                // Получаване на оригиналните стойности от базата данни
                const originalTotals = {};
                const totalElements = totalsContainer.querySelectorAll('[data-total-code]');

                totalElements.forEach(element => {
                    const code = element.getAttribute('data-total-code');
                    const valueElement = element.querySelector('[data-total-value]');
                    if (valueElement) {
                        originalTotals[code] = parseFloat(valueElement.getAttribute('data-total-value')) || 0;
                    }
                });

                // Изчисляване на новите стойности
                let newSubtotal = subtotal;
                let newTax = originalTotals.tax || 0;
                let newShipping = originalTotals.shipping || 0;
                let newTotal = newSubtotal + newTax + newShipping;

                // Ако има междинна сума в оригиналните данни, използваме пропорцията
                if (originalTotals.sub_total && originalTotals.sub_total > 0) {
                    const ratio = newSubtotal / originalTotals.sub_total;
                    newTax = (originalTotals.tax || 0) * ratio;
                    newTotal = newSubtotal + newTax + newShipping;
                }

                // Актуализиране на показаните стойности
                totalElements.forEach(element => {
                    const code = element.getAttribute('data-total-code');
                    const valueElement = element.querySelector('[data-total-value]');

                    if (valueElement) {
                        let newValue = 0;

                        switch (code) {
                            case 'sub_total':
                                newValue = newSubtotal;
                                break;
                            case 'tax':
                                newValue = newTax;
                                break;
                            case 'shipping':
                                newValue = newShipping;
                                break;
                            case 'total':
                                newValue = newTotal;
                                break;
                            default:
                                newValue = originalTotals[code] || 0;
                        }

                        // Форматиране на стойността
                        valueElement.textContent = newValue.toFixed(2) + ' лв.';
                        valueElement.setAttribute('data-total-value', newValue.toFixed(2));
                    }
                });
            },

            /**
             * Настройване на infinite scroll за предложенията
             */
            setupInfiniteScroll: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');

                // Премахване на предишни event listeners
                suggestionsDiv.removeEventListener('scroll', BackendModule.handleInfiniteScroll);

                // Добавяне на нов event listener
                suggestionsDiv.addEventListener('scroll', BackendModule.handleInfiniteScroll);
            },

            /**
             * Обработка на infinite scroll
             */
            handleInfiniteScroll: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');
                const scrollTop = suggestionsDiv.scrollTop;
                const scrollHeight = suggestionsDiv.scrollHeight;
                const clientHeight = suggestionsDiv.clientHeight;

                // Проверка дали сме близо до дъното (в рамките на 50px)
                if (scrollTop + clientHeight >= scrollHeight - 50) {
                    if (BackendModule.productSearch &&
                        BackendModule.productSearch.hasMore &&
                        !BackendModule.productSearch.isLoading) {

                        const query = BackendModule.productSearch.query || '';
                        if (query) {
                            BackendModule.searchProducts(query, true);
                        } else {
                            BackendModule.loadMoreInitialProducts();
                        }
                    }
                }
            },

            /**
             * Зареждане на още първоначални продукти
             */
            loadMoreInitialProducts: function() {
                if (!BackendModule.productSearch ||
                    !BackendModule.productSearch.hasMore ||
                    BackendModule.productSearch.isLoading) {
                    return;
                }

                const start = BackendModule.productSearch.currentPage * 10;

                const formData = new FormData();
                formData.append('limit', '10');
                formData.append('start', start.toString());

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts&user_token=' + BackendModule.config.userToken;

                BackendModule.productSearch.isLoading = true;
                BackendModule.showProductLoadingIndicator();

                fetch(searchUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.productSearch.hasMore = data.has_more || false;
                    BackendModule.productSearch.currentPage++;
                    BackendModule.displayProductSuggestions(data.products || [], false);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                })
                .catch(error => {
                    console.error('Error loading more products:', error);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                });
            },

            /**
             * Показване на loading индикатор за продукти
             */
            showProductLoadingIndicator: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');
                let loadingDiv = suggestionsDiv.querySelector('.loading-indicator');

                if (!loadingDiv) {
                    loadingDiv = document.createElement('div');
                    loadingDiv.className = 'loading-indicator p-3 text-center text-gray-500 text-sm';
                    loadingDiv.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Зареждане...';
                    suggestionsDiv.appendChild(loadingDiv);
                }
            },

            /**
             * Скриване на loading индикатор за продукти
             */
            hideProductLoadingIndicator: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');
                const loadingDiv = suggestionsDiv.querySelector('.loading-indicator');

                if (loadingDiv) {
                    loadingDiv.remove();
                }
            },

            /**
             * Показване на съобщение специфично за поръчки
             */
            showOrderAlert: function(type, message, duration = 3) {
                // Използваме същата функция като в основния модул, но с префикс за поръчки
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message, duration);
                } else {
                    // Fallback ако основната функция не е налична
                    alert(message);
                }
            }
        });

        // Инициализиране на productSearch обекта
        BackendModule.productSearch = {
            currentPage: 0,
            hasMore: true,
            isLoading: false,
            query: ''
        };
    }

})();
